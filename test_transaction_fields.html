<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Transaction Fields Test</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <style>
        body { background: #2c2e2f; color: #fff; padding: 20px; }
        .modal-content { background: #27292a !important; }
        .tab-content > .active {border-top: 1px solid #6aa50d; padding-top: 5px;}
        .transaction-field { margin-bottom: 15px; }
        .transaction-field .panel-heading { padding: 8px 15px; }
        .transaction-field .panel-title { font-size: 14px; margin: 0; }
        .transaction-field .panel-body { padding: 15px; }
        .options-container { margin-top: 15px; padding-top: 15px; border-top: 1px solid #555; }
        .option-item { margin-bottom: 8px; }
        .field-label { font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <h2>Transaction Fields Demo</h2>
        <p>This demonstrates the new Transaction Fields tab functionality.</p>
        
        <div class="panel panel-default" style="background: #3a3c3d; border-color: #555;">
            <div class="panel-heading" style="background: #4a4c4d; border-color: #555;">
                <h3 class="panel-title" style="color: #fff;">Transaction Fields</h3>
            </div>
            <div class="panel-body">
                <div class="form-group">
                    <button type="button" class="btn btn-success btn-sm" onclick="addTransactionField()">
                        <i class="fa fa-plus"></i> Add Field
                    </button>
                </div>
                <div id="transaction-fields-container">
                    <!-- Transaction fields will be added here dynamically -->
                </div>
            </div>
        </div>
    </div>

    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/js/bootstrap.min.js"></script>
    <script>
        let transactionFieldCounter = 0;
        let optionCounter = 0;

        function addTransactionField() {
            transactionFieldCounter++;
            let fieldId = 'field_' + transactionFieldCounter;
            
            let fieldHtml = `
                <div class="panel panel-default transaction-field" id="${fieldId}" style="background: #3a3c3d; border-color: #555;">
                    <div class="panel-heading" style="background: #4a4c4d; border-color: #555;">
                        <h4 class="panel-title" style="color: #fff;">
                            <span class="field-label">New Field</span>
                            <button type="button" class="btn btn-danger btn-xs pull-right" onclick="removeTransactionField('${fieldId}')">
                                <i class="fa fa-trash"></i> Remove
                            </button>
                            <div class="clearfix"></div>
                        </h4>
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Field Label*</label>
                                    <input type="text" class="form-control field-label-input" name="transaction_fields[${fieldId}][label]" 
                                           placeholder="Enter field label" onchange="updateFieldLabel('${fieldId}', this.value)">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Field Type*</label>
                                    <select class="form-control field-type-select" name="transaction_fields[${fieldId}][type]" 
                                            onchange="handleFieldTypeChange('${fieldId}', this.value)">
                                        <option value="">Select Type</option>
                                        <option value="TEXT">Text</option>
                                        <option value="SINGLE_CHOICE">Single Choice</option>
                                        <option value="MULTI_CHOICE">Multiple Choice</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Required</label>
                                    <div class="checkbox" style="margin-top: 7px;">
                                        <label style="color: #fff;">
                                            <input type="checkbox" name="transaction_fields[${fieldId}][is_required]" value="1"> 
                                            Required Field
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row options-container" id="options_${fieldId}" style="display: none;">
                            <div class="col-md-12">
                                <label>Options*</label>
                                <div class="options-list" id="options_list_${fieldId}">
                                    <!-- Options will be added here -->
                                </div>
                                <button type="button" class="btn btn-info btn-sm" onclick="addOption('${fieldId}')">
                                    <i class="fa fa-plus"></i> Add Option
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            $('#transaction-fields-container').append(fieldHtml);
        }

        function removeTransactionField(fieldId) {
            $('#' + fieldId).remove();
        }

        function updateFieldLabel(fieldId, label) {
            $('#' + fieldId + ' .field-label').text(label || 'New Field');
        }

        function handleFieldTypeChange(fieldId, type) {
            let optionsContainer = $('#options_' + fieldId);
            
            if (type === 'SINGLE_CHOICE' || type === 'MULTI_CHOICE') {
                optionsContainer.show();
                // Add default options if none exist
                if ($('#options_list_' + fieldId + ' .option-item').length === 0) {
                    addOption(fieldId);
                    addOption(fieldId);
                }
            } else {
                optionsContainer.hide();
                $('#options_list_' + fieldId).empty();
            }
        }

        function addOption(fieldId) {
            optionCounter++;
            let optionId = 'option_' + fieldId + '_' + optionCounter;
            
            let optionHtml = `
                <div class="input-group option-item" id="${optionId}" style="margin-bottom: 5px;">
                    <input type="text" class="form-control" name="transaction_fields[${fieldId}][options][]" 
                           placeholder="Enter option text">
                    <span class="input-group-btn">
                        <button type="button" class="btn btn-danger btn-sm" onclick="removeOption('${optionId}')">
                            <i class="fa fa-trash"></i>
                        </button>
                    </span>
                </div>
            `;
            
            $('#options_list_' + fieldId).append(optionHtml);
        }

        function removeOption(optionId) {
            $('#' + optionId).remove();
        }
    </script>
</body>
</html>
