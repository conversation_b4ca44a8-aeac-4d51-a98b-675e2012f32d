<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Kds_model extends CI_Model
{
  function datatables($selectedOtl)
  {
    $this->datatables->select("kds.*, o.name AS outlet_name");
    $this->datatables->from("setting_kitchen_display kds");
    $this->datatables->join("outlets o", "o.outlet_id=kds.outlet_fkid");
    $this->datatables->where("o.admin_fkid", $this->session->userdata('admin_id'));
    if ($selectedOtl != null) {
      $this->datatables->where_in("o.outlet_id", $selectedOtl);
    }

    return $this->datatables->generate();
  }

  function get_product_subcategories()
  {
    $this->db->select("psc.*");
    $this->db->from("products_subcategory psc");
    $this->db->where("psc.admin_fkid", $this->session->userdata('admin_id'));
    return $this->db->get();
  }

  function deleteKds($id)
  {
    $this->db->where("setting_kitchen_display_id", $id);
    return $this->db->delete("setting_kitchen_display");
  }

  function updateKds($id, $data)
  {
    $this->db->where("setting_kitchen_display_id", $id);
    return $this->db->update("setting_kitchen_display", $data);
  }

  function get_outlets_opts()
  {
    $this->db->select("o.outlet_id, o.name");
    $this->db->from("outlets o");
    $this->db->where("o.admin_fkid", $this->session->userdata('admin_id'));
    $this->db->where("o.data_status", "on");

    return $this->db->get();
  }
}
