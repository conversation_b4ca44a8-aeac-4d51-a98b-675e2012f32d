<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Outlet_model extends CI_Model
{

	protected $table = 'outlets';
	protected $id = 'outlet_id';

	function datatables()
	{
		$this->datatables->select('
			outlet_id AS id, name, address, phone, country, province, city, postal_code
		');
		$this->datatables->from('outlets');
		$this->datatables->where('admin_fkid', $this->session->userdata('admin_id'));
		$this->datatables->where('data_status', 'on');
		return $this->datatables->generate();
	}

	function get_all()
	{
		$this->db->distinct();
		$this->db->select('o.*');
		$this->db->from($this->table.' o');
		if ($this->session->userdata('user_type') == 'employee') {
			$this->db->join('employee_outlet eo', 'o.outlet_id = eo.outlet_fkid', 'left');
			$this->db->where('eo.employee_fkid', $this->session->userdata('user_id')); 
		}
		$this->db->where('o.admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
		$this->db->where('o.data_status', 'on'); //cari hanya data aktif
		$this->db->order_by('o.name', 'asc');
		return $this->db->get($this->table);
	}

	function create($data)
	{
		$data['admin_fkid'] = $this->session->userdata('admin_id');
		return $this->db->insert($this->table, $data);
	}

	function update($outlet_id, $data)
	{
		$this->db->where($this->id, $outlet_id);
		$this->db->where('admin_fkid', $this->session->userdata('admin_id'));
		return $this->db->update($this->table, $data);
	}

	function createbatch_workinghour($data)
	{
		return $this->db->insert_batch($this->table . '_workinghour', $data);
	}

	function createbatch_socialmedia($data)
	{
		return $this->db->insert_batch($this->table . '_socialmedia', $data);
	}

	function get_by_id($id)
	{
		$this->db->where($this->id, $id);
		$this->db->where('data_status', 'on'); //cari hanya data aktif
		$this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
		return $this->db->get($this->table);
	}

	function get_by_ids($ids = [])
	{
		$this->db->where_in($this->id, $ids);
		$this->db->where('data_status', 'on'); //cari hanya data aktif
		$this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
		return $this->db->get($this->table);
	}

	function get_socialmedia($outlet_id)
	{
		$this->db->where('outlet_fkid', $outlet_id);
		return $this->db->get('outlets_socialmedia');
	}

	function get_workinghour($outlet_id)
	{
		$this->db->where('outlet_fkid', $outlet_id);
		return $this->db->get('outlets_workinghour');
	}

	function delete($id)
	{
		//set debug
		$this->db->db_debug = FALSE;

		//delete query
		$this->db->where($this->id, $id);
		$this->db->where('data_status', 'on'); //cari hanya data aktif
		$this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
		return $this->db->delete($this->table);
	}

	function delete_workinghour_by_outletID($outlet_id)
	{
		$this->db->where('outlet_fkid', $outlet_id);
		return $this->db->delete($this->table . '_workinghour');
	}

	function delete_socialmedia_by_outletID($outlet_id)
	{
		$this->db->where('outlet_fkid', $outlet_id);
		return $this->db->delete($this->table . '_socialmedia');
	}

	function get_sales_tag_outlet($outlet_id)
	{
		$this->db->select("st.*, sto.outlet_fkid");
		$this->db->from("sales_tag_outlet sto");
		$this->db->join("sales_tag st", "st.sales_tag_id=sto.sales_tag_fkid");
		$this->db->where("sto.outlet_fkid", $outlet_id);
		$this->db->where("st.admin_fkid", $this->session->userdata('admin_id'));

		return $this->db->get();
	}

	function update_sales_tag($data)
	{
		foreach ($data as $value) {
			$this->db->where("sales_tag_id", $value->sales_tag_id);
			$this->db->where("admin_fkid", $value->admin_fkid);
			$this->db->update("sales_tag", [
				"name" => $value->name,
				"data_status" => $value->data_status,
				"date_modified" => round(microtime(true) * 1000)
			]);
		}
	}

	function insert_sales_tag($data, $outlet_id)
	{
		$insert_ids = [];
		$data_insert_sales_tag_outlet = [];
		foreach ($data as $value) {
			$insert = $this->db->insert("sales_tag", $value);
			if ($insert) {
				array_push($insert_ids, $this->db->insert_id());
			}
		}
		if (count($insert_ids) > 0) {
			foreach ($insert_ids as $id) {
				array_push($data_insert_sales_tag_outlet, [
					"sales_tag_fkid" => $id,
					"outlet_fkid" => $outlet_id
				]);
			}

			$inserts = $this->db->insert_batch("sales_tag_outlet", $data_insert_sales_tag_outlet);
		}
	}

	function delete_sales_tag($sales_tag_id)
	{
		$this->db->where("sales_tag_fkid", $sales_tag_id);
		$delete_sales_tag_outlet = $this->db->delete("sales_tag_outlet");
		if ($delete_sales_tag_outlet) {
			$this->db->where("sales_tag_id");
			$delete_sales_tag = $this->db->delete("sales_tag");
			return $delete_sales_tag;
		} else {
			return false;
		}
	}
}

/* End of file Outlet_model.php */
/* Location: ./application/modules/outlet_list/models/Outlet_model.php */