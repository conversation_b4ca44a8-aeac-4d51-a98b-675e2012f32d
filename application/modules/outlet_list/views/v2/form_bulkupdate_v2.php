<style>
/* MODAL TAB */
.tab-content > .active {border-top: 1px solid #6aa50d; padding-top: 5px;}
</style>

<form class="form-horizontal" id="form-bulkupdate">
<div class="modal fade" id="modal-form-bulkupdate" data-backdrop="static" style="overflow-y: scroll;">
	<div class="modal-dialog modal-lg">
		<div class="modal-content" style="background: #27292a;">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-label="Close">
				<span aria-hidden="true">&times;</span></button>
				<h4 class="modal-title">Bulk Update</h4>
			</div>
			<div class="modal-body">
				<div class="alert alert-warning">
					<strong>PERHATIAN!</strong> Silakan baca terlebih dahulu petunjuk berikut sebelum melakukan <b>BULK UPDATE</b>.
					<ul>
						<li>- Pilih Outlet yang akan diupdate.</li>
						<li>- Isikan fitur outlet yang akan diaktifkan pada outlet yang dipilih pada tab `Outlet Feature`.</li>
						<li>- Isikan informasi nota pada outlet yang dipilih pada tab `Receipt Information`.</li>
						<li>- Untuk `Receipt Logo` bila tidak diisi akan menggunakan logo sebelumnya yang terdapat pada outlet yang dipilih (bila ada).</li>
					</ul>
				</div>
	
				<div class="form-group">
					<label class="col-md-3 control-label">Select Outlet to Update*</label>
					<div class="col-md-9">
						<select class="form-control selectpicker" title="Choose Outlet" data-live-search="true" multiple="true" data-actions-box="true" name="outlets[]">
							<?php foreach ($list_outlets as $outlet): ?>
							<option value="<?=$outlet['outlet_id'] ?>"><?=htmlentities($outlet['name']) ?></option>
							<?php endforeach ?>
						</select>
						<div class="text-danger" data-error="outlet_ids"></div>
					</div>
				</div>


				<ul class="nav nav-tabs tab-uniq">
					<li class="active">
						<a style="color: #fff;" data-toggle="tab" href="#tab-bulkupdate-outletfeature">
							1) Outlet Feature
						</a>
					</li>
					<li>
						<a style="color: #fff;" data-toggle="tab" href="#tab-bulkupdate-receipt">
							2) Receipt Information
						</a>
					</li>
				</ul>
				<div class="tab-content">
					<div id="tab-bulkupdate-outletfeature" class="tab-pane fade in active" style="color: #fff; margin-bottom: -5px;">
						<div class="small text-italic"><i>*Will be apply to selected outlet.</i></div>
						<table class="table table-bordered table-report" id="outlet-feature-modal">
							<thead>
								<tr>
									<th>Feature Name</th>
									<th class="text-center"><input type="checkbox" onchange="outletFeatureSelectAll($(this))"></th>
								</tr>
							</thead>
							<tbody>
								<?php foreach ($list_outlet_feature as $key => $menu): ?>
								<tr class="treegrid-<?=$menu['key']?>">
									<td>
										<?=$menu['text'] ?>
										<?php if (!empty($menu['description'])): ?>
											<i class="fa fa-info-circle" data-toggle="tooltip" title="<?=htmlentities($menu['description']) ?>"></i>
										<?php endif ?>
									</td>
									<td class="text-center"><input type="checkbox" data-lv1="<?=$menu['key'] ?>" onchange="outletFeatureLv1($(this))"></td>
								</tr>
								<?php foreach ($menu['sub'] as $key2 => $menu2): ?>
								<tr class="treegrid-<?=$menu['key']?>-<?=$menu2['key']?> treegrid-parent-<?=$menu['key'] ?>">
									<td>
										<?=$menu2['text'] ?>
										<?php if (!empty($menu2['description'])): ?>
											<i class="fa fa-info-circle" data-toggle="tooltip" title="<?=htmlentities($menu2['description']) ?>"></i>
										<?php endif ?>
									</td>
									<td class="text-center"><input type="checkbox" data-lv1="<?=$menu['key'] ?>" data-lv2="<?=$menu2['key'] ?>"></td>
								</tr>
								<?php endforeach ?>
								<?php endforeach ?>
							</tbody>
						</table>
					</div>
					<div id="tab-bulkupdate-receipt" class="tab-pane fade" style="color: #fff; margin-bottom: -5px;">
						<div class="row">
							<div class="col-md-8">
								<div class="col-sm-12">
									<div class="form-group">
										<label>Receipt Note:</label> <span class="text-danger" data-error="receipt_note"></span>
										<textarea class="form-control" name="receipt_note" placeholder="Receipt Note (Optional)"></textarea>
										<div class="small text-italic"><i>*Will be apply to selected outlet.</i></div>
									</div>
									<div class="form-group">
										<label>Receipt Phone:</label> <span class="text-danger" data-error="receipt_phone"></span>
										<input type="text" name="receipt_phone" class="form-control" placeholder="Receipt Phone (Optional)">
										<div class="small text-italic"><i>*Will be apply to selected outlet.</i></div>
									</div>
									<div class="form-group">
										<label>Receipt Address:</label> <span class="text-danger" data-error="receipt_address"></span>
										<input type="text" name="receipt_address" class="form-control" placeholder="Receipt Address (Optional)">
										<div class="small text-italic"><i>*Will be apply to selected outlet.</i></div>
									</div>
									<div class="form-group">
										<label>Receipt Social Media:</label> <span class="text-danger" data-error="receipt_socialmedia"></span>
										<textarea name="receipt_socialmedia" class="form-control" placeholder="Receipt Social Media (Optional)"></textarea>
										<div class="small text-italic"><i>*Will be apply to selected outlet.</i></div>
									</div>
								</div>
							</div>
							<div class="col-md-4">
								<div class="col-sm-12">
									<div class="form-group">
										<label>Receipt Logo (max. 100KB):</label>
										<div class="text-danger" data-error="receipt_logo"></div>
										<input type="file" name="receipt_logo" accept=".jpg,.jpeg,.png" onchange="logo_preview(this)">
										<div class="small text-italic"><i>*Will be apply to selected outlet when filled.</i></div>
										<div style="clear: both;"></div>
										<div id="bulkupdate_receipt_logo" class="img-wrap"></div>
										<input type="hidden" name="receipt_logo_url">
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="modal-footer">
				<button type="reset" class="btn btn-default pull-left" onclick="modalBulkUpdate_resetForm()">Reset</button>
				<button type="submit" class="btn btn-primary">Save</button>
			</div>
		</div>
	</div>
</div>
</form>


<script type="text/javascript">
function logo_preview(input) {
	if (input.files && input.files[0]) {
		let reader = new FileReader();

		reader.onload = function(e) {
			//show preview
			$('#bulkupdate_receipt_logo').html('\
				<span class="close" onclick="logo_preview_remove()">&times;</span> \
				<img src="' + e.target.result + '" style="max-width: 270px"/>\
			').show();

			//set hidden url
			$('#form-bulkupdate [name=receipt_logo_url]').val(e.target.result)

			//hide input file
			$('#form-bulkupdate [name=receipt_logo]').hide()
		}

		reader.readAsDataURL(input.files[0]);
	}
}
function logo_preview_remove() {
	$('#form-bulkupdate #bulkupdate_receipt_logo').html('').hide(); //hide image preview
	$('#form-bulkupdate [name=receipt_logo_url]').val(''); //delete hidden image url
	$('#form-bulkupdate [name=receipt_logo]').val('').show(); //reset input value
}

function modalBulkUpdate_resetOutletFeatureModal() {
	$('#outlet-feature-modal [data-lv2]').prop('disabled', true);
}
function modalBulkUpdate_resetForm() {
	let form = $('#form-bulkupdate');
	form.find('.selectpicker').val('').selectpicker('refresh');
	modalBulkUpdate_resetOutletFeatureModal();
	logo_preview_remove();
}
</script>

<!-- OUTLET FEATURE -->
<script>
//init treegrid: outlet feature
$('#outlet-feature-modal').treegrid({
    initialState: 'collapsed'
});

function outletFeatureSelectAll(e) {
    let table = $('#outlet-feature-modal');
    let val = e.prop('checked');
    console.log('select all val -> '+val);

    //enable / disable checkbox
    table.find('[type=checkbox][data-lv2]').prop('disabled', !val);

    //check all
    table.find('[type=checkbox]').prop('checked', val);
}
function outletFeatureLv1(e) {
    let table = $('#outlet-feature-modal');
    let val = e.prop('checked');
    let data_lv1 = e.data('lv1');
    console.log('select lv1 '+data_lv1+' -> '+val);

    //enable lv1 + lv2
    table.find('[type=checkbox][data-lv1='+data_lv1+'][data-lv2]').prop('checked', val).prop('disabled', !val);
}
function outletFeatureReset() {
    $('#outlet-feature-modal [data-lv2]').prop('disabled', true);
    $('#outlet-feature-modal').treegrid('collapseAll');
}
</script>

<script type="text/javascript">
//modal when show
$('#modal-form-bulkupdate').on('shown.bs.modal', function () {
	$('#form-bulkupdate .selectpicker').selectpicker('refresh');
	modalBulkUpdate_resetOutletFeatureModal();
});


//send form event
$('#form-bulkupdate').submit(function(e) {
	e.preventDefault();
	$(document).off('focusin.modal'); //remove lock modal

	let form = $('#form-bulkupdate');
	let form_url = '<?=$form_url ?>';
	let formData = new FormData(this);

	// Ambil semua nilai outlet[]
	let outletValues = form.find('[name="outlets[]"]').val() || []; // ambil array selected values, atau array kosong jika none

	// Tambahkan ke FormData sebagai outlet_ids
	formData.append('outlet_ids', outletValues.join(','));


	// handle outlet feature
    let data_object = {};
    let elof = $('#tab-bulkupdate-outletfeature');
    let el = elof.find('[type=checkbox][data-lv1]:checked');
    el.each(function(i,k) {
        let data_lv1 = $(k).data('lv1');
        let data_lv2 = $(k).data('lv2');

        //get lv2
        let el_lv2 = elof.find('[type=checkbox][data-lv1='+data_lv1+'][data-lv2]');
        if (data_lv2 != undefined) {
            if (data_object[data_lv1] === true || data_object[data_lv1] == undefined) {
                data_object[data_lv1] = {};
            }

            data_object[data_lv1][data_lv2] = true;
        }else{
            data_object[data_lv1] = true;
        }
    });
    // console.log(data_object);
    formData.append('feature', JSON.stringify(data_object));


	let cleanedFormData = new FormData();
	// Salin semua field kecuali outlet[]
	for (let [key, value] of formData.entries()) {
		if (key.startsWith('feature[') || key === 'outlets[]') {
			continue; // skip
		}
		cleanedFormData.append(key, value);
	}

	$.ajax({
		url: form_url,
		type: 'PATCH',
		data: cleanedFormData,//formData,
		processData: false,
		contentType: false,
		dataType: 'json',
		beforeSend: function(xhr) {
			loading_show();

			xhr.setRequestHeader('Authorization', getUniqToken());

			//reset error
			form.find('[data-error]').text('');
		},
		success: function(response, status, xhr) {
			Swal.fire('Success!', `${response.message}`, status).then(function() {
				$('#modal-form-bulkupdate').modal('hide');
			});
		},
		error: function(xhr, status, message) {
			let response = xhr.responseJSON;
			let response_status = xhr.status;

			//handle server error
			if (response_status >= 500) {
				Swal.fire('Oops..!', `[${response_status}] ${message}`, 'error');
				return;
			}

			//handle server invalid response
			if (response == undefined) {
				Swal.fire('Oops..!', 'Server Error ['+response_status+']', 'error');
				return;
			}

			//handle error response
			Swal.fire('Oops..!', response.message, 'error');
			let data = response.errors;
			if (data != undefined) {
				Object.keys(data).forEach(function(k) {
					form.find('[data-error='+k+']').text(data[k])
				})
			}
		}
	}).always(function() {
		loading_hide();
	})
})
</script>