<link rel="stylesheet" type="text/css" href="<?=themes_url() ?>plugins/sweetalert2/dist/sweetalert2.min.css">
<script src="<?=themes_url() ?>plugins/sweetalert2/dist/sweetalert2.min.js"></script>

<style>
/* IMAGE PREVIEW REMOVE BUTTON */
.img-wrap {
    position: relative;
    display: inline-block;
    font-size: 0;
}
.img-wrap .close {
    position: absolute;
    top: 2px;
    right: 2px;
    z-index: 100;
    background-color: #FFF;
    padding: 5px 2px 2px;
    color: #000;
    font-weight: bold;
    cursor: pointer;
    opacity: .2;
    text-align: center;
    font-size: 22px;
    line-height: 10px;
    border-radius: 50%;
}
.img-wrap:hover .close {
    opacity: 1;
}


/* GMAP SEARCH CSS */
.controls {
    margin-top: 16px;
    border: 1px solid transparent;
    border-radius: 2px 0 0 2px;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    height: 32px;
    outline: none;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

#pac-input {
    background-color: #fff;
    font-family: Roboto;
    font-size: 15px;
    font-weight: 300;
    margin-left: 12px;
    padding: 0 11px 0 13px;
    text-overflow: ellipsis;
    width: 365px;
}
#pac-input:focus {
    border-color: #4d90fe;
}

.pac-container {
    font-family: Roboto;
}

#type-selector {
    color: #fff;
    background-color: #4d90fe;
    padding: 5px 11px 0px 11px;
}

#type-selector label {
    font-family: Roboto;
    font-size: 13px;
    font-weight: 300;
}

/* fix map search result */
.pac-container,
.pac-item {
    z-index: 2147483647 !important;
}


/* OUTLET FEATURE */
/* treegrid css */
table.mytreegrid thead tr th:nth-child(2),
table.mytreegrid tbody tr td:nth-child(2) {
    text-align: center;
}
</style>

<div class="container-fluid">
    <div class="content-uniq">
        <table class="table table-responsive table-report" id="mytable" width="100%">
            <thead>
                <tr>
                    <th width="50px">No</th>
                    <th>Name</th>
                    <th>Address</th>
                    <th>Phone</th>
                    <th>Country</th>
                    <th>Province</th>
                    <th>City</th>
                    <th>Postal Code</th>
                    <th width="50px">#</th>
                </tr>
            </thead>
        </table>
    </div>
</div><!-- /.container-fluid -->

<!-- Modal -->
<div class="modal fade" id="myModal" data-backdrop="static">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content" style="background: #27292a;">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">Modal Title</h4>
            </div>
            <form class="form-horizontal" method="" action="" data-url-api="<?=$url_api ?>">
                <div class="modal-body">
                    <ul class="nav nav-tabs nav-justified tab-uniq">
                        <li class="active"><a data-toggle="tab" href="#tab1">Basic*</a></li>
                        <li><a data-toggle="tab" href="#tab2">Feature</a></li>
                        <li><a data-toggle="tab" href="#tab3">Receipt</a></li>
                        <li><a data-toggle="tab" href="#tab4">Other</a></li>
                    </ul>
                    <div class="tab-content" style="margin-top: 10px">
                        <div id="tab1" class="tab-pane fade in active" style="color: #fff;">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label>Name*:</label> <span class="text-danger" data-error="name"></span>
                                    <input type="text" class="form-control" name="name" placeholder="Outlet Name">
                                </div>
                                <div class="form-group">
                                    <label>Outlet Location:</label> <span class="text-danger" data-error="location"></span>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <input type="hidden" name="location_lat">
                                                <input type="hidden" name="location_long">

                                                <input id="pac-input" class="controls form-control" type="text" placeholder="Search Box">
                                                <div id="map" style="min-height: 315px; width: 99%; height:100%"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Address*:</label> <span class="text-danger" data-error="address"></span>
                                            <div class="input-group">
                                                <input type="text" class="form-control" name="address" placeholder="Address">
                                                <span style="cursor: pointer;" class="input-group-addon" title="Click to Get from Location" id="getPosition"><i class="fa fa-map-marker"></i></span>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label>Country:</label>
                                            <input type="text" name="country" class="form-control" placeholder="Country">
                                            <div class="text-danger" data-error="country"></div>
                                        </div>
                                        <div class="form-group">
                                            <label>Province:</label>
                                            <input type="text" name="province" class="form-control" placeholder="Province">
                                            <div class="text-danger" data-error="province"></div>
                                        </div>
                                        <div class="form-group">
                                            <label>City:</label>
                                            <input type="text" name="city" class="form-control" placeholder="City">
                                            <div class="text-danger" data-error="city"></div>
                                        </div>
                                        <div class="form-group">
                                            <label>Postal Code:</label>
                                            <input type="text" name="postal_code" class="form-control" placeholder="Postal Code">
                                            <div class="text-danger" data-error="postal_code"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label>Phone*:</label> <span class="text-danger" data-error="phone"></span>
                                    <input type="text" class="form-control" name="phone" placeholder="Phone">
                                </div>
                                <div class="form-group">
                                    <label>Show on App (CRM Feature)*:</label> <span class="text-danger" data-error="app_show"></span>
                                    <select class="form-control" name="app_show">
                                        <option value="0">No</option>
                                        <option value="1">Yes</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>Outlet Logo (max. 100KB):</label>
                                    <input type="file" name="outlet_logo" accept=".jpg,.jpeg,.png">
                                    <div class="text-danger" data-error="outlet_logo"></div>

                                    <!-- logo preview -->
                                    <div id="outlet_logo" class="img-wrap"></div>
                                    <input type="hidden" name="outlet_logo_url">
                                </div>

                                <div class="form-group">
                                    <label>Working Hours:</label> <span class="text-danger" data-error="workinghour"></span>
                                    <select class="form-control" name="workinghour">
                                        <option value="">- Select Working -</option>
                                        <option value="alwaysopen">Always Open</option>
                                        <option value="selectedhour">Selected Hour</option>
                                    </select>
                                    <table class="table table-responsive table-report" data-workinghour="alwaysopen">
                                        <thead>
                                            <tr>
                                                <th>Day</th>
                                                <th>Open</th>
                                                <th>Active</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($list_day as $key => $day_name): ?>
                                            <tr>
                                                <td><?=$day_name ?></td>
                                                <td data-open="<?= $key ?>">Closed</td>
                                                <td><input type="checkbox" name="workinghour_alwaysopen[<?= $key ?>]" data-day="<?= $key ?>"></td>
                                            </tr>
                                            <?php endforeach ?>
                                        </tbody>
                                    </table>
                                    <table class="table table-responsive table-report" data-workinghour="selectedhour">
                                        <thead>
                                            <tr>
                                                <th>Day</th>
                                                <th>Open</th>
                                                <th>Active</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($list_day as $key => $day_name): ?>
                                            <tr>
                                                <td><?=$day_name ?></td>
                                                <td data-open="<?= $key ?>">Closed</td>
                                                <td><input type="checkbox" name="workinghour_selectedhour_active[<?= $key ?>]" data-day="<?= $key ?>"></td>
                                            </tr>
                                            <?php endforeach ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div id="tab2" class="tab-pane fade" style="color: #fff;">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label>Outlet Feature (for POS device):</label> <span class="text-danger" data-error="feature"></span>
                                    <table class="table table-bordered table-report" id="outlet-feature">
                                        <thead>
                                            <tr>
                                                <th>Feature Name</th>
                                                <th class="text-center"><input type="checkbox" onchange="outletFeatureSelectAll($(this))"></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($list_outlet_feature as $key => $menu): ?>
                                            <tr class="treegrid-<?=$menu['key']?>">
                                                <td>
                                                    <?=$menu['text'] ?>
                                                    <?php if (!empty($menu['description'])): ?>
                                                        <i class="fa fa-info-circle" data-toggle="tooltip" title="<?=htmlentities($menu['description']) ?>"></i>
                                                    <?php endif ?>
                                                </td>
                                                <td class="text-center"><input type="checkbox" data-lv1="<?=$menu['key'] ?>" onchange="outletFeatureLv1($(this))"></td>
                                            </tr>
                                            <?php foreach ($menu['sub'] as $key2 => $menu2): ?>
                                            <tr class="treegrid-<?=$menu['key']?>-<?=$menu2['key']?> treegrid-parent-<?=$menu['key'] ?>">
                                                <td>
                                                    <?=$menu2['text'] ?>
                                                    <?php if (!empty($menu2['description'])): ?>
                                                        <i class="fa fa-info-circle" data-toggle="tooltip" title="<?=htmlentities($menu2['description']) ?>"></i>
                                                    <?php endif ?>
                                                </td>
                                                <td class="text-center"><input type="checkbox" data-lv1="<?=$menu['key'] ?>" data-lv2="<?=$menu2['key'] ?>"></td>
                                            </tr>
                                            <?php endforeach ?>
                                            <?php endforeach ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div id="tab3" class="tab-pane fade" style="color: #fff;">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label>Receipt Note:</label> <span class="text-danger" data-error="receipt_note"></span>
                                            <textarea class="form-control" name="receipt_note" placeholder="Receipt Note (Optional)"></textarea>
                                        </div>
                                        <div class="form-group">
                                            <label>Receipt Phone:</label> <span class="text-danger" data-error="receipt_phone"></span>
                                            <div class="input-group">
                                                <input type="text" name="receipt_phone" class="form-control" placeholder="Receipt Phone (Optional)">
                                                <span style="cursor: pointer;" class="input-group-addon" title="Click to Get from Outlet" id="getOutletPhone"><i class="fa fa-crosshairs"></i></span>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label>Receipt Address:</label> <span class="text-danger" data-error="receipt_address"></span>
                                            <div class="input-group">
                                                <input type="text" name="receipt_address" class="form-control" placeholder="Receipt Address (Optional)">
                                                <span style="cursor: pointer;" class="input-group-addon" title="Click to Get from Outlet" id="getOutletAddress"><i class="fa fa-crosshairs"></i></span>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label>Receipt Social Media:</label> <span class="text-danger" data-error="receipt_socialmedia"></span>
                                            <textarea name="receipt_socialmedia" class="form-control" placeholder="Receipt Social Media (Optional)"></textarea>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label>Receipt Logo (max. 100KB):</label>
                                            <input type="file" name="receipt_logo" accept=".jpg,.jpeg,.png">
                                            <div class="text-danger" data-error="receipt_logo"></div>

                                            <!-- logo preview -->
                                            <div id="receipt_logo" class="img-wrap"></div>
                                            <input type="hidden" name="receipt_logo_url">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div id="tab4" class="tab-pane fade" style="color: #fff;">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label>Sharing Commission:</label> <span class="text-danger" data-error="commission_sharing"></span>
                                    <div class="input-group">
                                        <input type="text" class="form-control" name="commission_sharing" placeholder="Commission on Percentage" min="0" max="100">
                                        <span class="input-group-addon">%</span>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label>Social Media:</label>
                                    <table class="table table-bordered table-report" id="table-socialmedia">
                                        <thead>
                                            <tr>
                                                <th>Social Name</th>
                                                <th>Account</th>
                                                <th width="50px"><a href="javascript:socialMediaAdd()" class="btn btn-xs btn-primary pull-right">Add</a></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>Facebook</td>
                                                <td colspan="2"><input type="text" name="socialmedia[facebook]" data-socialmedia="facebook" class="form-control" placeholder="Facebook Account"></td>
                                            </tr>
                                            <tr>
                                                <td>Instagram</td>
                                                <td colspan="2"><input type="text" name="socialmedia[instagram]" data-socialmedia="instagram" class="form-control" placeholder="Instagram Account"></td>
                                            </tr>
                                            <tr>
                                                <td>Line</td>
                                                <td colspan="2"><input type="text" name="socialmedia[line]" data-socialmedia="line" class="form-control" placeholder="Line Account"></td>
                                            </tr>
                                            <tr>
                                                <td>Twitter / X</td>
                                                <td colspan="2"><input type="text" name="socialmedia[twitter]" data-socialmedia="twitter" class="form-control" placeholder="Twitter / X Account"></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="form-group">
                                    <label>Order Type:</label>
                                    <table class="table table-bordered table-report" id="table-salestag">
                                        <thead>
                                            <tr>
                                                <th>Tag Name</th>
                                                <th width="50px"><a href="javascript:salesTagAdd()" class="btn btn-xs btn-primary pull-right">Add</a></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td colspan="2" class="text-center">No Data</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div style="clear: both;"></div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-primary">Save</button>
                </div>
            </form>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<!-- Ajax Dynamic Content -->
<div id="ajax-content"></div>


<!-- DATATABLE LOAD START -->
<script>
let url_datatable = '<?=$url_datatable ?>';
$(document).ready(function() {
    //numbering data
    $.fn.dataTableExt.oApi.fnPagingInfo = function(oSettings)
    {
        return {
            "iStart": oSettings._iDisplayStart,
            "iEnd": oSettings.fnDisplayEnd(),
            "iLength": oSettings._iDisplayLength,
            "iTotal": oSettings.fnRecordsTotal(),
            "iFilteredTotal": oSettings.fnRecordsDisplay(),
            "iPage": Math.ceil(oSettings._iDisplayStart / oSettings._iDisplayLength),
            "iTotalPages": Math.ceil(oSettings.fnRecordsDisplay() / oSettings._iDisplayLength)
        };
    };

    //init datatable
    let table = '#mytable';
    var t = $(table).DataTable({
        dom: 'l<"toolbar">frtip', //buat custom toolbar
        initComplete: function() {
                var api = this.api();
                $(table+'_filter input').off('.DT').on('keyup.DT', function(e) {
                    if (e.keyCode == 13) {
                        api.search(this.value).draw();
                    }
                });

                //tombol add
                $(table+'_wrapper > div.toolbar').html(''+
                '<div class="btn-group pull-right">'+
                    '<button type="button" class="btn uniq-add" onclick="btnAdd()" style="margin-left:5px; padding:4px 10px;"><span class="fa fa-plus"></span> Add New</button>'+
                    '<button type="button" class="btn btn-info" onclick="btnEditBulk()" style="margin-left:0px; padding:4px 10px;"><span class="fa fa-edit"></span> Bulk Update</button>'+
                '</div>');
            },
            language: {
             processing: loading_datatable(),
            },
            processing: true,
            serverSide: true,
            ajax: {
                url: url_datatable,
                type: "GET",
                dataType: 'json',
                beforeSend: function(xhr) {
                    xhr.setRequestHeader('Authorization', getUniqToken());
                },
                data: function(d) {
                    //filter advance digunakan kalau kolom advance filter expanded
                    return $.extend({}, d, {
                        // outlet: filter_outlet,
                    });
                },
                error: function(xhr, status, error) {
                    if (xhr.status == 401) {
                        return jqRefreshToken(this, xhr);
                    }
                    alert(error)
                },
            },
            columns: [
                { data: 'id'},
                { data: 'name'},
                { data: 'address'},
                { data: 'phone'},
                { data: 'country'},
                { data: 'province'},
                { data: 'city'},
                { data: 'postal_code'},
                {
                    "orderable": false,
                    "searchable": false,
                    "className": "text-center",
                    "render": function(data, type, row) {
                        let btn = '';
                        btn += '<button class="btn btn-xs btn-warning" onclick="btnEdit('+row.id+')"><i class="fa fa-edit"></i></button>';
                        btn += ' ';
                        btn += '<button class="btn btn-xs btn-danger"  onclick="btnDelete('+row.id+')"><i class="fa fa-trash"></i></button>';
                        return btn;
                    }
                }
            ],
            order: [[1, 'asc']],
            rowCallback: function(row, data, iDisplayIndex) {
                var info = this.fnPagingInfo();
                var page = info.iPage;
                var length = info.iLength;
                var index = page * length + (iDisplayIndex + 1);
                $('td:eq(0)', row).html(index);
            }
        }
    );
    //end datatable init
})
</script>

<script>
//fix bug swal pop-up on modal
$.fn.modal.Constructor.prototype.enforceFocus = function() {};

function btnAdd() {
    // set form
    let form = $('#myModal form');

    // reset form
    let method = form.attr('method');
    if (method != 'post') {
        form.find('.nav-tabs a:first').tab('show');
        form[0].reset();
        form.find('[data-error]').text('');
    }

    //reset element outlet feature
    if (method != 'post') {
        resetTableWorkingHour();
        outletFeatureReset();
        resetPreviewImageURL();
        $('#table-socialmedia [data-type=custom]').remove();
        $('#table-salestag tbody').html('<tr><td colspan="2" class="text-center">No Data</td></tr>');
    }

    // set form element
    let url_api = form.data('url-api');
    form.attr('method','post').attr('action', url_api);

    // set modal
    $('#myModal .modal-title').text('Create Outlet');
    $('#myModal').modal('show');
}
function btnEdit(id) {
    <?php if ($permission['edit']): ?>
    loading_show();
    let url_api = '<?= $url_api ?>/' + id;
    $.ajax({
        type: 'get',
        url: url_api,
        dataType: 'json',
        beforeSend: function(xhr) {
            xhr.setRequestHeader('Authorization', getUniqToken());
        },
        success: function(response, status, xhr) {
            let data = response.data;

            let form = $('#myModal form');

            //reset form
            form.find('.nav-tabs a:first').tab('show');
            form[0].reset();
            form.find('[data-error]').text('');
            resetTableWorkingHour();
            outletFeatureReset();
            resetPreviewImageURL();
            $('#table-socialmedia [data-type=custom]').remove();
            $('#table-salestag tbody').html('<tr><td colspan="2" class="text-center">No Data</td></tr>');

            //set form element
            form.attr('method','patch').attr('action', url_api);

            //set data to form
            form.find('[name=name]').val(data.name);
            // map marker
            form.find('[name=location_lat]').val(data.location.lat);
            form.find('[name=location_long]').val(data.location.long);
            if (data.location != undefined) {
                remove_marker();
                var latlng = JSON.parse('{"lat": ' + data.location.lat + ', "lng": ' + data.location.long + '}');
                // var latlng = '{'+this.lat+ ','+ this.long +'}';
                // set marker with drag response for every marker
                // Create a marker for each place.
                var marker = new google.maps.Marker({
                    map: map,
                    position: latlng,
                    draggable: true,
                });
                marker.setPosition(latlng);
                map.setCenter(latlng);
                markers.push(marker);

                // set marker with drag response for every marker
                markers.forEach(function(marker) {
                    google.maps.event.addListener(marker, 'dragend', function(e) {
                        latlongToForm(this.getPosition());
                        // map.setCenter(this.getPosition()); // Set map center to marker position
                    });
                });
            }


            form.find('[name=address]').val(data.address);
            form.find('[name=country]').val(data.country);
            form.find('[name=province]').val(data.province);
            form.find('[name=city]').val(data.city);
            form.find('[name=postal_code]').val(data.postal_code);
            form.find('[name=phone]').val(data.phone);
            form.find('[name=app_show]').val(data.app_show);
            if (data.outlet_logo != "") {
                $('#outlet_logo').html('\
                    <span class="close" onclick="removePreviewImageURL(\'outlet_logo\')">&times;</span> \
                    <img src="' + data.outlet_logo + '" style="max-width: 300px"/>\
                ').show();
                form.find('[name=outlet_logo_url]').val(data.outlet_logo);
                form.find('[name=outlet_logo]').hide();
            }
            form.find('[name=workinghour]').val(data.workinghour).change();
            Object.entries(data.workinghour_list).forEach(function(k,i) {
                let day = k[0];
                let times = k[1];

                //checked active working hour
                let element = form.find('table[data-workinghour='+data.workinghour+']');
                element.find('input[data-day='+day+']').prop('checked', true);

                //set time
                switch(data.workinghour) {
                case 'alwaysopen':
                    element.find('[data-open='+day+']').html('24 hours');
                    break;
                case 'selectedhour':
                    element.find('[data-open='+day+']').html('\
                        <div> \
                            <a href="javascript:void(0)" data-day="' + day + '" onclick="add_set_hour($(this))"> \
                                <small><i class="fa fa-plus"></i> Add a Set of Hour</small> \
                            </a> \
                        </div> \
                    ');
                    times.forEach(function(k2,i2) {
                        let value = k2.time_open + ' - ' + k2.time_close;
                        add_hour_input(element.find('[data-open='+day+']'), day, value);
                    });
                    break;
                default:
                    break;
                }
            });

            //feature
            Object.entries(data.feature).forEach(function(k,i) {
                let key = k[0];
                let value = k[1];
                if (typeof(value) === 'object') {
                    $('#outlet-feature .treegrid-' + key + ' [data-lv1='+key+']').prop('checked',true);
                    $('#outlet-feature [data-lv1='+key+']').prop('disabled', false);

                    Object.entries(value).forEach(function(k2,i2) {
                        let key2 = k2[0];
                        let value2 = k2[1];
                        $('#outlet-feature [data-lv1=' + key + '][data-lv2=' + key2 + '][type=checkbox]').prop('checked', value2);
                    });
                } else {
                    $('#outlet-feature .treegrid-' + key + ' input[type=checkbox]').prop('checked', value);
                }
            });

            //receipt
            form.find('[name=receipt_note]').val(data.receipt.receipt_note);
            form.find('[name=receipt_phone]').val(data.receipt.receipt_phone);
            form.find('[name=receipt_address]').val(data.receipt.receipt_address);
            form.find('[name=receipt_socialmedia]').val(data.receipt.receipt_socialmedia);
            if (data.receipt.receipt_logo != '') {
                $('#receipt_logo').html('\
                    <span class="close" onclick="removePreviewImageURL(\'receipt_logo\')">&times;</span> \
                    <img src="' + data.receipt.receipt_logo + '" style="max-width: 300px"/>\
                ').show();
                form.find('[name=receipt_logo_url]').val(data.receipt.receipt_logo);
                form.find('[name=receipt_logo]').hide();
            }

            //commission
            form.find('[name=commission_sharing]').val(data.commission_sharing);

            //social media
            Object.entries(data.socialmedia).forEach(function(k,i) {
                let key = k[0];
                let value = k[1];

                $('#table-socialmedia').find('[data-socialmedia='+key+']').val(value);

                if (key == 'other') {
                    Object.entries(value).forEach(function(k2,i2) {
                        let key2 = k2[0];
                        let value2 = k2[1];
                        let CM = currentMillis() + '-' + makeid(10);

                        $('#table-socialmedia tbody').append('\
                            <tr data-type="custom">\
                                <td><input type="text" name="socialmedia['+CM+'][name]" data-custom="key" data-id="'+CM+'" class="form-control" placeholder="Custom Social Name" required="" value="'+key2+'"></td>\
                                <td><input type="text" name="socialmedia['+CM+'][value]" data-custom="value" data-id="'+CM+'" class="form-control" placeholder="Custom Social Address" required="" value="'+value2+'"></td>\
                                <td class="text-center"><a href="javascript:void(0)" onclick="socialMediaDelete($(this))" class="btn btn-danger btn-xs"><i class="fa fa-trash"></i></a></td>\
                            </tr>');
                    });
                }
            });

            //sales tag
            if (data.sales_tags.length > 0) {
                $('#table-salestag tbody').html('');
                data.sales_tags.forEach(function(k,i) {
                    let tag_id = k.id;
                    let tag_name = k.name;
                    let CM1 = currentMillis() + '-' + makeid(10);
                    let CM = tag_id;

                    $('#table-salestag tbody').append('\
                        <tr>\
                            <td><input type="text" name="salestag['+CM+'][name]" data-custom="name" data-id="'+CM+'" class="form-control" placeholder="Sales Tag" required="" value="'+tag_name+'"></td>\
                            <td class="text-center"><a href="javascript:void(0)" onclick="salesTagDelete($(this))" class="btn btn-danger btn-xs"><i class="fa fa-trash"></i></a></td>\
                        </tr>\
                    ');
                });
            }

            // set modal
            $('#myModal .modal-title').text('Update Outlet');
            $('#myModal').modal('show');
        },
        error: function(xhr, status, error) {
            let response = xhr.responseJSON;
            let response_status = xhr.status;

            //handle server error
            if (response_status >= 500) {
                Swal.fire(error+' ['+response_status+']', 'Please try again or contact our support!', 'error');
                return;
            }

            //handle server invalid response
            if (response == undefined) {
                Swal.fire('Oops..!', 'Server Error ['+xhr.status+']', 'error');
                return;
            }

            //handle error response
            Swal.fire('Oops..!', response.message, 'error');
        }
    }).always(function() {
        loading_hide();
    });
    <?php else: ?>
    Swal('Access Forbidden!', 'Please contact your administrator.', 'error');
    <?php endif ?>
}
function btnEditBulk() {
    let modal_url = '<?=$url_modal_bulkupdate ?>';
    $.ajax({
        url: modal_url,
        beforeSend: function() {
            loading_show();
        },
        success: function(response, status, xhr) {
            $('#ajax-content').html(response);
            $('#modal-form-bulkupdate').modal('show');
        },
        error: function(xhr, status, message) {
            let response_status = xhr.status;
            let response = xhr.responseText;
            let rJSON = xhr.responseJSON;
            if (rJSON != undefined) {
                Swal.fire('Oops..!', rJSON.message, 'error');
                return;
            }

            Swal.fire('Oops..!', `[${response_status}] ${message}`, status);
        }
    }).always(function() {
        loading_hide();
    });
}
function btnDelete(id) {
    <?php if ($permission['delete']) : ?>
    let url = '<?= $url_api ?>/' + id;
    Swal.fire({
        title: 'Are you sure?',
        text: "You won't be able to revert this!",
        type: 'warning',
        showCancelButton: true,
        focusCancel: true,
        cancelButtonColor: '#d33',
        confirmButtonColor: '#3085d6',
        confirmButtonText: 'Yes, delete it!',
        showLoaderOnConfirm: true,
        allowOutsideClick: () => !Swal.isLoading(),
        preConfirm: function() {
            $.ajax({
                type: "DELETE",
                url: url,
                dataType: 'json',
                beforeSend: function(xhr) {
                    xhr.setRequestHeader('Authorization', getUniqToken());
                },
                success: function(response, status, xhr) {
                    Swal('Success!', response.message, 'success');
                    $('#mytable').DataTable().ajax.reload(null, false); //reload datatables
                },
                error: function(xhr, status, error) {
                    let response = xhr.responseJSON;
                    let response_status = xhr.status;

                    //handle server error
                    if (response_status >= 500) {
                        Swal.fire(error+' ['+response_status+']', 'Please try again or contact our support!', 'error');
                        return;
                    }

                    //handle server invalid response
                    if (response == undefined) {
                        Swal.fire('Oops..!', 'Server Error ['+xhr.status+']', 'error');
                        return;
                    }

                    //handle error response
                    Swal.fire('Oops..!', response.message, 'error');
                }
            });
        }
    })
    <?php else: ?>
    Swal('Access Forbidden!', 'Please contact your administrator.', 'error');
    <?php endif ?>
}
</script>

<script>
/* IMAGE PREVIEW */
function showPreviewImageURL(input, element) {
    let form = $('#myModal form');

    if (input.files && input.files[0]) {
        var reader = new FileReader();

        reader.onload = function(e) {
            // $('#outlet_logo').attr('src', e.target.result);

            $('#' + element).html('\
            <span class="close" onclick="removePreviewImageURL(\'' + element + '\')">&times;</span> \
            <img src="' + e.target.result + '" style="max-width: 300px"/>\
        ').show();

            //set hidden url
            form.find('[name=' + element + '_url]').val(e.target.result)

            //hide input file
            form.find('[name=' + element + ']').hide()
        }

        reader.readAsDataURL(input.files[0]);
    }
}
function removePreviewImageURL(element) {
    let form = $('#myModal form');

    //init
    var text = '';
    if (element == 'outlet_logo') {
        text = 'Outlet Logo';
    } else if (element == 'receipt_logo') {
        text = 'Receipt Logo';
    }


    //konfirm delete
    var c = confirm('Remove ' + text + '?');
    if (c) {
        form.find('[name=' + element + '_url]').val(''); //delete hidden image url
        form.find('#' + element).html('').hide(); //hide image preview
        form.find('[name=' + element + ']').val('').show(); //reset input value
    }
}
function resetPreviewImageURL() {
    resetPreviewImageURL_element('outlet_logo');
    resetPreviewImageURL_element('receipt_logo');
}
function resetPreviewImageURL_element(element) {
    let form = $('#myModal form');
    form.find('[name=' + element + '_url]').val(''); //delete hidden image url
    form.find('#' + element).html('').hide(); //hide image preview
    form.find('[name=' + element + ']').val('').show(); //reset input value
}
</script>

<script>
$('#myModal form #getPosition').click(function() {
    $('#you_location_img').click();
});
$('#myModal form [name=outlet_logo]').change(function() {
    showPreviewImageURL(this, 'outlet_logo');
});
$('#myModal form [name=receipt_logo]').change(function() {
    showPreviewImageURL(this, 'receipt_logo');
});
$('#myModal form [name=workinghour]').change(function() {
    //hide working hour element
    $('#myModal form table[data-workinghour]').hide();

    //show element by value
    let value = $(this).val();
    if (value != '') {
        $('#myModal form table[data-workinghour='+value+']').show();
    }
});
$('#myModal form [data-workinghour=alwaysopen] [type=checkbox]').change(function() {
    let day = $(this).data('day');
    let element = $('#myModal form [data-workinghour=alwaysopen] [data-open=' + day + ']');

    //set text
    let is_checked = $(this).prop('checked');
    if (is_checked == true) {
        element.html('24 hours');
    } else {
        element.html('Closed');
    }
});
$('#myModal form [data-workinghour=selectedhour] [type=checkbox]').change(function() {
    let day = $(this).data('day');
    let is_checked = $(this).prop('checked');

    let a = $(this).parent().parent().find('[data-open=' + day + ']');

    if (is_checked == true) {
        let p = a.children('div input').length;

        //reset inputan
        if (p == 0) {
            a.html(' \
            <div> \
                <a href="javascript:void(0)" data-day="' + day + '" onclick="add_set_hour($(this))"> \
                    <small><i class="fa fa-plus"></i> Add a Set of Hour</small> \
                </a> \
            </div> \
        ');
        }

        //init add hour input
        add_hour_input(a, day);
    } else {
        a.html('Closed');
    }
});
$('#myModal form #getOutletPhone').click(function() {
    let value = $('#myModal form [name=phone]').val();
    $('#myModal form [name=receipt_phone]').val(value);
});
$('#myModal form #getOutletAddress').click(function() {
    let value = $('#myModal form [name=address]').val();
    $('#myModal form [name=receipt_address]').val(value);
});

$('#myModal form').submit(function(e) {
    e.preventDefault();

    let form = $(this);
    let method = form.attr('method');
    let url = form.attr('action');
    // let formData = $(this).serialize();
    let formData = new FormData(this);

    //get working hour
    let working_object = {};
    let elwo = form.find('[name=workinghour]').val();
    if (elwo != '') {
        let days = form.find('[data-workinghour='+elwo+'] [data-open]');
        switch(elwo){
        case 'alwaysopen':
            days.each(function(k,i) {
                let day = $(this).data('open');
                let isActive = form.find('[data-workinghour='+elwo+'] input[name="workinghour_alwaysopen['+day+']"]').prop('checked');
                if (isActive) {
                    let hours = [];
                    hours.push('00:00 - 23:59');
                    working_object[day] = hours;
                }
            })
            break;
        case 'selectedhour':
            days.each(function(k,i) {
                let day = $(this).data('open');
                let isActive = form.find('[data-workinghour='+elwo+'] input[name="workinghour_selectedhour_active['+day+']"]').prop('checked');
                if (isActive) {
                    // Jika aktif, ambil jam kerja untuk hari tersebut
                    let hours = [];
                    // Ambil semua input yang sesuai dengan hari tersebut
                    form.find('[data-workinghour='+elwo+'] input[name="workinghour_selectedhour['+day+'][]"]').each(function() {
                        hours.push($(this).val()); // Simpan jam kerja
                    });
                    working_object[day] = hours;
                }
            });
            break;
        default:
            break;
        }
        // console.log(working_object)
        formData.append('workinghour', JSON.stringify(working_object));
    }

    //get outlet feature
    let data_object = {};
    let el = $('#outlet-feature [type=checkbox][data-lv1]:checked');
    el.each(function(i,k) {
        let data_lv1 = $(k).data('lv1');
        let data_lv2 = $(k).data('lv2');

        //get lv2
        let el_lv2 = $('#outlet-feature [type=checkbox][data-lv1='+data_lv1+'][data-lv2]');
        if (data_lv2 != undefined) {
            if (data_object[data_lv1] === true || data_object[data_lv1] == undefined) {
                data_object[data_lv1] = {};
            }

            data_object[data_lv1][data_lv2] = true;
        }else{
            data_object[data_lv1] = true;
        }
    });
    // console.log(data_object);
    formData.append('feature', JSON.stringify(data_object));

    //get social media
    let data_social = {};
    let el2 = $('#table-socialmedia').find('input[data-socialmedia]');
    el2.each(function(i,k) {
        let socmed_key = $(k).data('socialmedia');
        let socmed_val = $(k).val();

        if (socmed_val != "") {
            data_social[socmed_key] = socmed_val;
        }
    });
    let el2c = $('#table-socialmedia').find('input[data-custom=key]');
    el2c.each(function(i,k) {
        let socmed_id = $(k).data('id');
        let socmed_key = $(k).val();
        let socmed_val = el2c.parent().parent().find('[data-custom=value][data-id='+socmed_id+']').val();

        if (socmed_key != "" && socmed_val != "") {
            data_social[socmed_key] = socmed_val;
        }
    });
    // console.log(data_social);
    formData.append('socialmedia', JSON.stringify(data_social));

    //get sales tag
    let data_tag = [];
    let elst = $('#table-salestag').find('input[type=text]');
    elst.each(function(i,k) {
        let tag_id = $(k).data('id');
        let tag_name = $(k).val();

        data_tag.push({
            id: tag_id,
            name: tag_name,
        });
    });
    // console.log(data_tag);
    formData.append('sales_tag', JSON.stringify(data_tag));

    $.ajax({
        url: url,
        type: method,
        data: formData,
        processData: false,
        contentType: false,
        dataType: 'json',
        beforeSend: function(xhr) {
            loading_show();
            xhr.setRequestHeader('Authorization', getUniqToken());

            //reset form error
            form.find('[data-error]').text('');
        },
        success: function(response, status, xhr) {
            Swal.fire('Success', response.message, 'success');

            $('#mytable').DataTable().ajax.reload(null,false); //reload datatables

            //reset form
            form[0].reset();
            form.find('[data-error]').text('');
            if (method == 'post') {
                resetTableWorkingHour();
                outletFeatureReset();
            }

            //reset form component
            form.find('[name=outlet_logo]').show();
            form.find('#outlet_logo').hide();
            form.find('[name=receipt_logo]').show();
            form.find('#receipt_logo').hide();
            socialMediaReset();
            salesTagReset();


            if (method.toLowerCase() != 'post') {
                $('#myModal').modal('hide');
            }
        },
        error: function(xhr, status, error) {
            let response = xhr.responseJSON;
            let response_status = xhr.status;

            //handle server error
            if (response_status >= 500) {
                Swal.fire(error+' ['+response_status+']', 'Please try again or contact our support!', 'error');
                return;
            }

            //handle server invalid response
            if (response == undefined) {
                Swal.fire('Oops..!', 'Server Error ['+xhr.status+']', 'error');
                return;
            }

            //handle error response
            Swal.fire('Oops..!', response.message, 'error');
            let errors = response.errors;
            if (errors != undefined) {
                Object.keys(errors).forEach(function(k) {
                    form.find('[data-error='+k+']').text(errors[k]);
                });
            }
        }
    }).always(function() {
        loading_hide();
    })
});
</script>

<!-- GENERAL JS -->
<script>
function makeid(length) {
    var result = '';
    var characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    var charactersLength = characters.length;
    for (var i = 0; i < length; i++) {
        result += characters.charAt(Math.floor(Math.random() * charactersLength));
    }
    return result;
}
</script>

<!-- GMAPS JS -->
<script>
function latlongToForm(coor) {
    let form = $('#myModal form');
    form.find('[name=location_lat]').val(coor.lat());
    form.find('[name=location_long]').val(coor.lng());

    latlongToAddress(coor.lat(), coor.lng())
}

function latlongToAddress(lat, lng) {
    let form = $('#myModal form');

    var geocoder = new google.maps.Geocoder;
    var latlng = new google.maps.LatLng(lat, lng);
    geocoder.geocode({
        'latLng': latlng
    }, function(results, status) {
        if (status === google.maps.GeocoderStatus.OK) {
            if (results[0]) {
                //set to form
                var data = results[0];
                var addr = data.address_components;

                var street_number = "",
                    route = "",
                    fulladdr = "";

                addr.forEach(function(d) {
                    var long_name = d.long_name;
                    var type = d.types[0];

                    if (long_name != undefined || long_name != "undefined") {
                        switch (type) {
                            case "postal_code":
                                form.find('[name=postal_code]').val(long_name)
                                break;
                            case "country":
                                form.find('[name=country]').val(long_name)
                                break;
                            case "administrative_area_level_1":
                                form.find('[name=province]').val(long_name)
                                break;
                            case "administrative_area_level_2":
                                form.find('[name=city]').val(long_name)
                                break;
                            case "route":
                                route = long_name;
                                break;
                            case "street_number":
                                street_number = long_name;
                                break;
                            default:
                        }
                    }

                })

                //set full address
                fulladdr = route + " " + street_number;
                form.find('[name=address]').val(fulladdr);
            } else {
                alert('No results found');
            }
        } else {
            alert('Geocoder failed due to: ' + status);
        }
    });
}

function addYourLocationButton(map, marker) {
    var controlDiv = document.createElement('div');

    var firstChild = document.createElement('button');
    firstChild.type = 'button';
    firstChild.style.backgroundColor = '#fff';
    firstChild.style.border = 'none';
    firstChild.style.outline = 'none';
    firstChild.style.width = '40px';
    firstChild.style.height = '40px';
    firstChild.style.borderRadius = '2px';
    firstChild.style.boxShadow = '0px 1px 4px -1px rgba(0,0,0,0.3)';
    firstChild.style.cursor = 'pointer';
    firstChild.style.marginRight = '10px';
    firstChild.style.padding = '0px';
    firstChild.title = 'Your Location';
    controlDiv.appendChild(firstChild);

    var secondChild = document.createElement('div');
    secondChild.style.margin = 'auto';
    secondChild.style.width = '18px';
    secondChild.style.height = '18px';
    secondChild.style.backgroundImage = 'url(https://maps.gstatic.com/tactile/mylocation/mylocation-sprite-1x.png)';
    secondChild.style.backgroundSize = '180px 18px';
    secondChild.style.backgroundPosition = '0px 0px';
    secondChild.style.backgroundRepeat = 'no-repeat';
    secondChild.id = 'you_location_img';
    firstChild.appendChild(secondChild);

    google.maps.event.addListener(map, 'dragend', function() {
        $('#you_location_img').css('background-position', '0px 0px');
    });

    firstChild.addEventListener('click', function(e) {
        e.preventDefault(); //prevent submit when click my location

        var imgX = '0';
        var animationInterval = setInterval(function() {
            if (imgX == '-18') imgX = '0';
            else imgX = '-18';
            $('#you_location_img').css('background-position', imgX + 'px 0px');
        }, 500);
        if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(function(position) {
                var latlng = new google.maps.LatLng(position.coords.latitude, position.coords.longitude);

                // Clear out the old markers.
                markers.forEach(function(marker) {
                    marker.setMap(null);
                });
                markers = [];

                // Create a marker for each place.
                var marker = new google.maps.Marker({
                    map: map,
                    position: latlng,
                    draggable: true,
                });
                marker.setPosition(latlng);
                map.setCenter(latlng);
                markers.push(marker);

                // set marker with drag response for every marker
                markers.forEach(function(marker) {
                    google.maps.event.addListener(marker, 'dragend', function(e) {
                        latlongToForm(this.getPosition());
                        // map.setCenter(this.getPosition()); // Set map center to marker position
                    });
                });


                clearInterval(animationInterval);
                $('#you_location_img').css('background-position', '-144px 0px');

                var bounds = new google.maps.LatLngBounds();
                bounds.extend(latlng);
                map.setZoom(15);
                latlongToForm(latlng);
            });
        } else {
            clearInterval(animationInterval);
            $('#you_location_img').css('background-position', '0px 0px');
        }
    });

    controlDiv.index = 1;
    map.controls[google.maps.ControlPosition.RIGHT_BOTTOM].push(controlDiv);
}

var map;
var markers = [];

function remove_marker() {
    // Clear out the old markers.
    markers.forEach(function(marker) {
        marker.setMap(null);
    });
    markers = [];
}

function initMap() {
    //init map
    map = new google.maps.Map(document.getElementById('map'), {
        center: {
            lat: -7.797068,
            lng: 110.370529
        },
        zoom: 10,
        mapTypeControl: false,
        streetViewControl: false,
    });

    /* MY LOCATION */
    addYourLocationButton(map);


    /* SEARCH BOX START */
    // Create the search box and link it to the UI element.
    var input = document.getElementById('pac-input');
    var searchBox = new google.maps.places.SearchBox(input);
    map.controls[google.maps.ControlPosition.TOP_LEFT].push(input);

    // Bias the SearchBox results towards current map's viewport.
    map.addListener('bounds_changed', function() {
        searchBox.setBounds(map.getBounds());
    });

    // var markers = [];
    // Listen for the event fired when the user selects a prediction and retrieve
    // more details for that place.
    searchBox.addListener('places_changed', function() {
        var places = searchBox.getPlaces();

        if (places.length == 0) {
            return;
        }

        // Clear out the old markers.
        markers.forEach(function(marker) {
            marker.setMap(null);
        });
        markers = [];

        // For each place, get the icon, name and location.
        var bounds = new google.maps.LatLngBounds();
        places.forEach(function(place) {
            if (!place.geometry) {
                console.log("Returned place contains no geometry");
                return;
            }
            var icon = {
                url: place.icon,
                size: new google.maps.Size(71, 71),
                origin: new google.maps.Point(0, 0),
                anchor: new google.maps.Point(17, 34),
                scaledSize: new google.maps.Size(25, 25)
            };

            // Create a marker for each place.
            markers.push(new google.maps.Marker({
                map: map,
                // icon: icon,
                // title: place.name,
                position: place.geometry.location,
                draggable: true,
            }));

            /* MY CUSTOM SCRIPT START */
            // set marker with drag response for every marker
            markers.forEach(function(marker) {
                google.maps.event.addListener(marker, 'dragend', function(e) {
                    latlongToForm(this.getPosition());
                    // map.setCenter(this.getPosition()); // Set map center to marker position
                });
            });

            // set long lat of marker to form
            latlongToForm(place.geometry.location);
            /* MY CUSTOM SCRIPT END */

            if (place.geometry.viewport) {
                // Only geocodes have viewport.
                bounds.union(place.geometry.viewport);
            } else {
                bounds.extend(place.geometry.location);
            }
        });
        map.fitBounds(bounds);
    });

    // Prevent submit on result when select with enter
    google.maps.event.addDomListener(input, 'keydown', function(event) {
        if (event.keyCode === 13) {
            event.preventDefault();
        }
    });
    /* SEARCH BOX END */

    /* FIX SEARCH RESULT DROPDOWN START */
    document.onfullscreenchange = function(event) {
        let target = event.target;
        let pacContainerElements = document.getElementsByClassName("pac-container");
        if (pacContainerElements.length > 0) {
            let pacContainer = document.getElementsByClassName("pac-container")[0];
            if (pacContainer.parentElement === target) {
                // console.log("Exiting FULL SCREEN - moving pacContainer to body");
                document.getElementsByTagName("body")[0].appendChild(pacContainer);
            } else {
                // console.log("Entering FULL SCREEN - moving pacContainer to target element");
                target.appendChild(pacContainer);
            }
        } else {
            // console.log("FULL SCREEN change - no pacContainer found");

        }
    };
    /* FIX SEARCH RESULT DROPDOWN END */
}
</script>
<script src="https://maps.googleapis.com/maps/api/js?key=<?= $gmap_key ?>&libraries=places&callback=initMap" async defer></script>


<!-- OUTLET FEATURE -->
<script>
//init treegrid: outlet feature
$('#outlet-feature').treegrid({
    initialState: 'collapsed'
});

function outletFeatureSelectAll(e) {
    let table = $('#outlet-feature');
    let val = e.prop('checked');
    console.log('select all val -> '+val);

    //enable / disable checkbox
    table.find('[type=checkbox][data-lv2]').prop('disabled', !val);

    //check all
    table.find('[type=checkbox]').prop('checked', val);
}
function outletFeatureLv1(e) {
    let table = $('#outlet-feature');
    let val = e.prop('checked');
    let data_lv1 = e.data('lv1');
    console.log('select lv1 '+data_lv1+' -> '+val);

    //enable lv1 + lv2
    table.find('[type=checkbox][data-lv1='+data_lv1+'][data-lv2]').prop('checked', val).prop('disabled', !val);
}
function outletFeatureReset() {
    $('#outlet-feature [data-lv2]').prop('disabled', true);
    $('#outlet-feature').treegrid('collapseAll');
}
</script>

<!-- OUTLET WORKING HOUR -->
<script>
function resetTableWorkingHour() {
    $('#myModal form table[data-workinghour]').hide();
}
function add_set_hour(ini) {
    var day = ini.data('day');
    var element = ini.parent().parent().parent().find('[data-open=' + day + ']');
    add_hour_input(element, day);
}
function add_hour_input(element, day, value = '') {
    var a = element.find('div:last');
    var p = element.find('div input').length;
    var limit = 2; //jumlah input max
    if (p <= (limit - 1)) {
        var CM = currentMillis() + '-' + makeid(10);
        a.before('\
        <div class="input-group" data-id="' + CM + '"> \
            <input type="text" class="form-control" placeholder="Open" value="' + value + '" name="workinghour_selectedhour[' + day + '][]"> \
            <span style="cursor: pointer;" class="input-group-addon" onclick="remove_hour_input($(this))"><i class="fa fa-trash"></i></span> \
        </div>')

        //init daterange on input
        a.parent().find('[data-id=' + CM + '] input').daterangepicker({
            timePicker: true,
            timePicker24Hour: true,
            timePickerIncrement: 1,
            // timePickerSeconds: true,
            locale: {
                format: 'HH:mm'
            }
        }).on('show.daterangepicker', function(ev, picker) {
            picker.container.find(".calendar-table").hide();
        });

        //show set hour
        a.parent().find('div:last').show();
    }

    if (p >= (limit - 1)) {
        //hidden set hour
        a.parent().find('div:last').hide();
    }
}
function remove_hour_input(ini) {
    //init
    var element_block = ini.parent().parent();
    // var day = element_block.data('open');

    //hapus element
    var dataid = ini.parent().data('id');
    var element = element_block.find('[data-id=' + dataid + ']');
    var main_element = element.parent();

    //hapus element
    element.remove();

    //cek input length
    var a = main_element.find('div input').length;
    if (a <= 1) {
        //show set hour
        main_element.find('div:last').show();
    }
    if (a <= 0) {
        //centang active
        element_block.next().children('[type=checkbox]').prop('checked', false).change()
    }
}
</script>

<!-- SOCIAL MEDIA -->
<script>
function socialMediaAdd() {
    let element = $('#table-socialmedia tbody');
    let CM = currentMillis() + '-' + makeid(10);

    //add input
    element.append('\
        <tr data-type="custom">\
        <td><input type="text" name="socialmedia[' + CM + '][name]" data-custom="key" data-id="'+CM+'" class="form-control" placeholder="Custom Social Name" required></td>\
        <td><input type="text" name="socialmedia[' + CM + '][value]" data-custom="value" data-id="'+CM+'" class="form-control" placeholder="Custom Social Address" required></td>\
        <td class="text-center"><a href="javascript:void(0)" onclick="socialMediaDelete($(this))" class="btn btn-danger btn-xs"><i class="fa fa-trash"></i></a></td>\
        </tr>\
    ');
}
function socialMediaDelete(ini) {
    var row = ini.parent().parent();
    var row_body = row.parent();
    var l = row.parent().children('tr').length;
    row.remove();
}
function socialMediaReset() {
    $('#table-socialmedia').find('[data-type=custom]').remove();
}
</script>

<!-- SALES TAG -->
<script>
function salesTagAdd() {
    let element = $('#table-salestag tbody');
    let CM = currentMillis() + '-' + makeid(10);

    //cek input data
    let l = element.find('input').length;
    if (l <= 0) {
        //reset element
        element.html('');
    }

    //append input
    element.append('\
        <tr>\
        <td><input type="text" name="salestag[' + CM + '][name]" data-custom="name" data-id="'+CM+'" class="form-control" placeholder="Sales Tag" required></td>\
        <td class="text-center"><a href="javascript:void(0)" onclick="salesTagDelete($(this))" class="btn btn-danger btn-xs"><i class="fa fa-trash"></i></a></td>\
        </tr>\
    ');
}
function salesTagDelete(ini) {
    let row = ini.parent().parent();
    let row_body = row.parent();
    let l = row.parent().children('tr').length;
    row.remove();

    let element = $('#table-salestag tbody');
    let ld = element.find('input').length;
    if (ld <= 0) {
        //reset element
        element.html('<tr><td colspan="2" class="text-center">No Data</td></tr>');
    }
}
function salesTagReset() {
    $('#table-salestag tbody').html('<tr><td colspan="2" class="text-center">No Data</td></tr>');
}
</script>