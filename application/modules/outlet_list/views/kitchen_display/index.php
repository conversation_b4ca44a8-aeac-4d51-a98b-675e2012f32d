<div id="kds-app" class="container-fluid">
  <div class="content-uniq">
    <table class="table table-responsive table-report" id="table-kds" width="100%">

      <thead>
        <tr>
          <th width="80px">No</th>
          <th>Device Name</th>
          <th>IP Address</th>
          <th>Oulet</th>
          <th>Categories</th>
          <th>Action</th>
        </tr>
      </thead>
    </table>
  </div>
  <?php $this->load->view("kitchen_display/modal");  ?>
</div>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script type="text/javascript">
  var kdsApp = new Vue({
    el: "#kds-app",
    data: {
      table: "",
      updateId: "",
      categories: <?= $categories ?>,
      form: {
        data: {
          name: "",
          address: "",
          categories: [],
        },
        error: {
          name: "",
          address: "",
          categories: "",
        }
      },
    },
    mounted() {
      this.datatables()
    },
    methods: {
      datatables() {
        $(function() {
          let selectedOutlets = [];
          kdsApp.table = $("#table-kds").DataTable({
            dom: '<"pull-left"l><"toolbar">frtip',
            initComplete: function() {
              var api = this.api();
              $('#table-kds_filter input').off('.DT').on('keyup.DT', function(e) {
                if (e.keyCode == 13) {
                  api.search(this.value).draw();
                }
              });

              var opts = ""

              var outlets = <?= $outlets ?>;
              outlets.forEach((it) => {
                opts += `<option value="${it.outlet_id}">${it.name}</option>`
              })

              $("#table-kds_wrapper > div.toolbar").html('' +
                '<div class="btn-group pull-left" style="margin-left:2rem;"><select data-width="200px" id="select-outlet" data-actions-box="true" class="form-control selectpicker" multiple data-live-search="true" title="all outlets">' + opts + '</select></div>');

              $("#select-outlet").selectpicker()

              $('#select-outlet').on('hide.bs.select', function() {
                selectedOutlets = $("#select-outlet").val()
                kdsApp.table.ajax.reload()
              })
            },
            ajax: {
              url: "<?= $url_datatables ?>",
              type: "POST",
              data: function(d) {
                return $.extend({}, d, {
                  selected_outlets: selectedOutlets,
                })
              }
            },
            language: {
              processing: loading_datatable(),
              lengthMenu: '_MENU_',
            },
            processing: true,
            serverSide: true,
            columns: [{
              data: null,
              render: function(data, type, row, meta) {
                return meta.row + 1;
              },
            }, {
              data: "name"
            }, {
              data: "address"
            }, {
              data: "outlet_name"
            }, {
              data: "categories",
              orderable: false,
              searchable: false,
              render: function(data, type, row) {
                let categoryNames = JSON.parse(data).map((it) => it.category_name)
                let cNames = categoryNames.length > 2 ? categoryNames.slice(0, 2).join(", ") : categoryNames.join(", ")
                return `<span title="${categoryNames}">${cNames}${categoryNames.length > 2 ? " ...": ""}</span>`;
              }
            }, {
              orderable: false,
              searchable: false,
              className: "text-center",
              render: function(data, type, row) {
                var btn_edit = '<button class="btn btn-xs btn-success btn-edit"><i class="fa fa-edit"></i></button>';
                var btn_hapus =
                  '<button class="btn btn-xs btn-danger btn-delete"><i class="fa fa-trash"></i></button>';


                return btn_edit + ' ' + btn_hapus;
              }
            }]
          })

          $("#table-kds").on("click", ".btn-edit", function() {
            var data = $("#table-kds").DataTable().row($(this).closest('tr')).data();
            kdsApp.setFormValue(data)
            $("#modal-edit").modal('show');
          })

          $("#table-kds").on("click", ".btn-delete", function() {
            var data = $("#table-kds").DataTable().row($(this).closest('tr')).data();
            Swal.fire({
              title: "Are you sure?",
              text: "You won't be able to revert this!",
              icon: "warning",
              showCancelButton: true,
              confirmButtonColor: "#3085d6",
              cancelButtonColor: "#d33",
              confirmButtonText: "Yes, delete it!"
            }).then((result) => {
              kdsApp.deleteKitchenDisplay(data);
            });
          })
        })
      },
      closeModal() {
        $("#modal-edit").modal('hide');
      },
      setFormValue(data) {
        this.resetError()
        this.updateId = data.setting_kitchen_display_id;
        let cat = JSON.parse(data.categories).map((it) => it.category_id);

        this.form.data = {
          name: data.name,
          address: data.address,
          categories: cat,
        }

        this.$nextTick(() => {
          $('#modal-edit .selectpicker').selectpicker('render');
          $('#modal-edit .selectpicker').selectpicker('refresh');
        })
      },
      resetError() {
        this.form.error = {
          name: "",
          address: "",
          categories: "",
        }
      },
      deleteKitchenDisplay(data) {
        const urlDelete = `<?= $url_delete ?>/${data.setting_kitchen_display_id}`;
        loading_show()
        fetch(urlDelete).then((res) => {
          if (res.ok) {
            return res.json()
          } else {
            loading_hide();
            Swal.fire({
              title: "!Deleted",
              text: "Delete Kitchen Display Failed",
              icon: "error"
            });
          }
        }).then((data) => {
          if (data.status == 200) {
            Swal.fire({
              title: "Deleted!",
              text: data.message,
              icon: "success"
            });
          } else {
            Swal.fire({
              title: "!Deleted",
              text: data.message,
              icon: "error"
            });
          }
          loading_hide();
          kdsApp.table.ajax.reload()
          $("#modal-edit").modal('hide');
        })
      },
      submitForm() {
        if (this.form.data.name == "" || this.form.data.address == "" || this.form.data.categories.length == 0) {
          if (this.form.data.name == "") {
            this.form.error.name = "Kitchen display name must be filled"
          } else {
            this.form.error.name = ""
          }
          if (this.form.data.address == "") {
            this.form.error.address = "Kitchen display IP Address must be filled";
          } else {
            this.form.error.address = "";
          }
          if (this.form.data.categories.length == 0) {
            this.form.error.categories = "Select categories";
          } else {
            this.form.error.categories = "";
          }
          return;
        }

        const ipv4Regex = /^(25[0-5]|2[0-4][0-9]|[0-1]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[0-1]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[0-1]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[0-1]?[0-9][0-9]?)$/;

        if (!ipv4Regex.test(this.form.data.address)) {
          this.form.error.address = "Invalid IPv4 Address"
          return;
        } else {
          this.form.error.address = ""
        }
        loading_show();
        const urlUpdate = `<?= $url_update ?>/${this.updateId}`
        let formData = new FormData()
        const form = this.form.data
        formData.append("name", form.name)
        formData.append("address", form.address)
        formData.append("categories", form.categories)
        fetch(urlUpdate, {
          method: "POST",
          body: formData,
        }).then((res) => res.json()).then((data) => {
          if (data.status == 200) {
            Swal.fire({
              title: "Updated!",
              text: data.message,
              icon: "success"
            });
          } else {
            Swal.fire({
              title: "!Updated",
              text: data.message,
              icon: "error"
            });
          }
          loading_hide();
          kdsApp.table.ajax.reload()
          $("#modal-edit").modal('hide');
        })
      }
    }
  })
</script>