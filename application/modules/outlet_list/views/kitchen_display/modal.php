<div class="modal fade" id="modal-edit" role="dialog" data-backdrop="static">
  <div class="modal-dialog">
    <div class="modal-content" style="background: #27292a;">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span></button>
        <h4 class="modal-title">Edit Kitchen Display</h4>
      </div>
      <div class="form-horizontal">
        <div class="modal-body">
          <div class="form-group" style="margin-bottom: 1rem;">
            <label class="col-sm-3 control-label">Kitchen Name</label>
            <div class="col-sm-9">
              <input type="text" class="form-control" name="name" placeholder="Kds pos 1" v-model="form.data.name">
              <span class="text-danger">{{ form.error.name }}</span>
            </div>
          </div>
          <div class="form-group" style="margin-bottom: 1rem;">
            <label class="col-sm-3 control-label">IP Address</label>
            <div class="col-sm-9">
              <input type="text" class="form-control" id="ip-address-input" name="address" placeholder="e.g., ***********" v-model="form.data.address">
              <span class="text-danger">{{ form.error.address }}</span>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-3 control-label">Categories</label>
            <div class="col-sm-9">
              <select v-model="form.data.categories" name="categories" class="form-control selectpicker" multiple id="categories">
                <option :value="it.product_subcategory_id" v-for="it in categories">
                  {{it.name}}
                </option>
              </select>
              <span class="text-danger">{{ form.error.categories }}</span>
            </div>
          </div>
        </div>

        <div class="modal-footer">
          <button type="button" class="btn btn-warning" @click="closeModal()">Close</button>
          <button type="button" class="btn btn-success" @click="submitForm()">Update</button>
        </div>
      </div>
    </div>
  </div>
</div>