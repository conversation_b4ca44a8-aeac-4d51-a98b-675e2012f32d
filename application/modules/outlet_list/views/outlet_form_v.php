<style type="text/css">
	/* IMAGE PREVIEW REMOVE BUTTON */
	.img-wrap {
		position: relative;
		display: inline-block;
		font-size: 0;
	}

	.img-wrap .close {
		position: absolute;
		top: 2px;
		right: 2px;
		z-index: 100;
		background-color: #FFF;
		padding: 5px 2px 2px;
		color: #000;
		font-weight: bold;
		cursor: pointer;
		opacity: .2;
		text-align: center;
		font-size: 22px;
		line-height: 10px;
		border-radius: 50%;
	}

	.img-wrap:hover .close {
		opacity: 1;
	}

	/* GMAP SEARCH CSS */
	.controls {
		margin-top: 16px;
		border: 1px solid transparent;
		border-radius: 2px 0 0 2px;
		box-sizing: border-box;
		-moz-box-sizing: border-box;
		height: 32px;
		outline: none;
		box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
	}

	#pac-input {
		background-color: #fff;
		font-family: Roboto;
		font-size: 15px;
		font-weight: 300;
		margin-left: 12px;
		padding: 0 11px 0 13px;
		text-overflow: ellipsis;
		width: 400px;
	}

	#pac-input:focus {
		border-color: #4d90fe;
	}

	.pac-container {
		font-family: Roboto;
	}

	#type-selector {
		color: #fff;
		background-color: #4d90fe;
		padding: 5px 11px 0px 11px;
	}

	#type-selector label {
		font-family: Roboto;
		font-size: 13px;
		font-weight: 300;
	}

	/* fix map search result */
	.pac-container,
	.pac-item {
		z-index: 2147483647 !important;
	}

	.feature-info {
		margin-left: 5px;
		color: #777;
		cursor: help;
	}

	.tooltip-inner {
		max-width: 300px;
		text-align: left;
	}
</style>


<div class="content-uniq">
	<form class="form-horizontal" method="post" action="" id="myform">
		<div class="panel-group" id="accordion">
			<div class="panel panel-default">
				<div class="panel-heading">
					<h4 class="panel-title">
						<a data-toggle="collapse" data-parent="#accordion" href="#collapse1">
							Basic Information</a>
					</h4>
				</div>
				<div id="collapse1" class="panel-collapse collapse">
					<div class="panel-body">

						<div class="row">
							<div class="col-md-6">
								<div class="col-md-12">
									<input type="hidden" name="action">
									<input type="hidden" name="id">

									<div class="form-group">
										<label>Outlet Name*:</label> <span class="text-danger" data-error="name"></span>
										<input type="text" class="form-control" name="name" placeholder="Outlet Name">
									</div>
									<div class="form-group">
										<label>Address*:</label> <span class="text-danger" data-error="address"></span>
										<div class="input-group">
											<input type="text" class="form-control" name="address" placeholder="Address">
											<span style="cursor: pointer;" class="input-group-addon" title="Click to Get from Location" id="getPosition"><i class="fa fa-map-marker"></i></span>
										</div>
										<div class="row">
											<div class="col-md-6">
												<label>Country:</label>
												<input type="text" name="country" class="form-control" placeholder="Country">
											</div>
											<div class="col-md-6">
												<label>Province:</label>
												<input type="text" name="province" class="form-control" placeholder="Province">
											</div>

											<div class="col-md-6">
												<label>City:</label>
												<input type="text" name="city" class="form-control" placeholder="City">
											</div>
											<div class="col-md-6">
												<label>Postal Code:</label>
												<input type="text" name="postal_code" class="form-control" placeholder="Postal Code">
											</div>
										</div>
									</div>
									<div class="form-group">
										<label>Phone*:</label> <span class="text-danger" data-error="phone"></span>
										<input type="text" class="form-control" name="phone" placeholder="Phone">
									</div>
									<div class="form-group">
										<label>Show on App (CRM Feature)*:</label> <span class="text-danger" data-error="app_show"></span>
										<select class="form-control" name="app_show">
											<option value="1">Yes</option>
											<option value="0">No</option>
										</select>
										<!-- <input type="text" class="form-control" name="phone" placeholder="Phone"> -->
									</div>
									<div class="form-group">
										<label>Outlet Logo (max. 100KB):</label> <span class="text-danger" data-error="outlet_logo"></span>
										<input type="file" name="outlet_logo">
										<div style="clear: both;"></div>
										<div id="outlet_logo" class="img-wrap"></div>
										<input type="hidden" name="outlet_logo_url">
									</div>
								</div>
							</div>
							<div class="col-md-6">
								<div class="col-md-12">
									<div class="form-group">
										<label>Outlet Location:</label> <span class="text-danger" data-error="location"></span>
										<input type="hidden" name="lat">
										<input type="hidden" name="long">
									<!--
									</div>
									<div class="form-group">
									-->
										<input id="pac-input" class="controls form-control" type="text" placeholder="Search Box">
										<div id="map" style="min-height: 300px; width: 100%; height:100%"></div>
									</div>

									<div class="form-group">
										<label>Working Hours:</label> <span class="text-danger" data-error="workinghour"></span>
										<select class="form-control" name="workinghour">
											<option value="">- Select Working -</option>
											<option value="alwaysopen">Always Open</option>
											<option value="selectedhour">Selected Hour</option>
										</select>
										<table class="table table-responsive table-report" data-workinghour="alwaysopen">
											<thead>
												<tr>
													<th>Day</th>
													<th>Open</th>
													<th>Active</th>
												</tr>
											</thead>
											<tbody>
												<?php foreach ($daylist as $key => $value) : ?>
													<tr>
														<td><?= $value ?></td>
														<td data-open="<?= $key ?>">Closed</td>
														<td><input type="checkbox" name="workinghour_alwaysopen[<?= $key ?>]" data-day="<?= $key ?>"></td>
													</tr>
												<?php endforeach ?>
											</tbody>
										</table>
										<table class="table table-responsive table-report" data-workinghour="selectedhour">
											<thead>
												<tr>
													<th>Day</th>
													<th>Open</th>
													<th>Active</th>
												</tr>
											</thead>
											<tbody>
												<?php foreach ($daylist as $key => $value) : ?>
													<tr>
														<td><?= $value ?></td>
														<td data-open="<?= $key ?>">
															Closed
														</td>
														<td><input type="checkbox" name="workinghour_selectedhour_active[<?= $key ?>]" data-day="<?= $key ?>"></td>
													</tr>
												<?php endforeach ?>
											</tbody>
										</table>
									</div>
								</div>
							</div>
						</div>

					</div>
				</div>
			</div>
			<!-- panel more information -->
			<div class="panel panel-default">
				<div class="panel-heading">
					<h4 class="panel-title">
						<a data-toggle="collapse" data-parent="#accordion" href="#collapseSosmed">
							More Information (Optional)</a>
					</h4>
				</div>
				<div id="collapseSosmed" class="panel-collapse collapse">
					<div class="panel-body">

						<div class="col-md-12">
							<div class="form-group">
								<div class="row">
									<!-- social media -->
									<div class="col-md-12">
										<label>Social Media:</label>
									</div>
									<div class="col-md-12">
										<?php foreach ($sosmedlist as $key => $value) : ?>
											<div class="form-group">
												<label class="col-md-2 control-label"><?= $value ?></label>
												<div class="col-md-10">
													<?php if ($key != 'other') : ?>
														<input type="text" name="socialmedia[<?= $key ?>]" data-socialmedia="<?= $key ?>" class="form-control" placeholder="<?= $value ?> Account">
													<?php else : ?>
														<table class="table table-bordered table-report" id="table-socialmedia">
															<thead>
																<tr>
																	<th>Social Name</th>
																	<th>Social</th>
																	<th width="50px"><a href="javascript:void(0)" class="btn btn-xs btn-primary pull-right" data-action="add">Add</a></th>
																</tr>
															</thead>
															<tbody>
																<tr>
																	<td colspan="3" class="text-center">No Data</td>
																</tr>
															</tbody>
														</table>
													<?php endif ?>
												</div>
											</div>
										<?php endforeach ?>
									</div>
									<!-- social media -->

									<!-- outlet tags -->
									<div class="col-md-12">
										<label>Order Type:</label>
									</div>
									<div class="col-md-12">
										<div class="form-group">
											<label class="col-md-2"></label>
											<div class="col-md-10">
												<table id="table-tags" class="table table-bordered table-report">
													<thead>
														<tr>
															<th>Type</th>
															<th class="text-center" width="50px">
																On/Off
															</th>
															<th class="d-flex justify-content-center" width="50px"><a href="javascript:void(0)" type="button" class="btn btn-xs btn-primary add-type">Add</a></th>
														</tr>
													</thead>
												</table>
											</div>
										</div>
									</div>
									<!-- outlet tags -->
								</div>
							</div>
						</div>

					</div>
				</div>
			</div>
			<!-- panel more outlet feature -->
			<div class="panel panel-default">
				<div class="panel-heading">
					<h4 class="panel-title">
						<a data-toggle="collapse" data-parent="#accordion" href="#collapse2">
							Outlet Feature (Optional)</a>
					</h4>
				</div>
				<div id="collapse2" class="panel-collapse collapse">
					<div class="panel-body">

						<div class="col-md-12">
							<div class="form-group">
								<label>Outlet Feature (for POS device):</label> <span class="text-danger" data-error="feature"></span>
								<table class="table table-bordered table-report" id="outlet-feature">
									<thead>
										<tr>
											<th>Feature Name</th>
											<th class="text-center"><input type="checkbox" name="all_feature"></th>
										</tr>
									</thead>
									<tbody>
										<?php foreach ($featurelist as $key => $value) : ?>
											<?php if (!empty($featurelist2[$value[0]])) : ?>
												<tr class="treegrid-<?= $value[0] ?>">
														<td>
															<?= $value[1] ?>
															<?php if (!empty($value[2])): ?>
															<i class="fa fa-info-circle feature-info" data-toggle="tooltip" title="<?= htmlspecialchars($value[2]) ?>"></i>
															<?php endif; ?>
														</td>
													<td class="text-center"><input type="checkbox" name="feature[<?= $value[0] ?>]"></td>
												</tr>
												<?php foreach ($featurelist2[$value[0]] as $k2 => $v2) : ?>
													<tr class="treegrid-<?= $value[0] ?>-<?= $v2[0] ?> treegrid-parent-<?= $value[0] ?>">
															<td>
																<?= $v2[1] ?>
																<?php if (!empty($v2[2])): ?>
																<i class="fa fa-info-circle feature-info" data-toggle="tooltip" title="<?= htmlspecialchars($v2[2]) ?>"></i>
																<?php endif; ?>
															</td>
														<td class="text-center"><input type="checkbox" name="feature[<?= $value[0] ?>][<?= $v2[0] ?>]" data-lv1="<?= $value[0] ?>" data-lv2="<?= $v2[0] ?>"></td>
													</tr>
												<?php endforeach ?>
											<?php else : ?>
												<tr class="treegrid-<?= $value[0] ?>">
														<td>
															<?= $value[1] ?>
															<?php if (!empty($value[2])): ?>
															<i class="fa fa-info-circle feature-info" data-toggle="tooltip" title="<?= htmlspecialchars($value[2]) ?>"></i>
															<?php endif; ?>
														</td>
													<td class="text-center"><input type="checkbox" name="feature[<?= $value[0] ?>]"></td>
												</tr>
											<?php endif ?>
										<?php endforeach ?>
									</tbody>
								</table>
							</div>
						</div>

					</div>
				</div>
			</div>
			<!-- panel receipt information -->
			<div class="panel panel-default">
				<div class="panel-heading">
					<h4 class="panel-title">
						<a data-toggle="collapse" data-parent="#accordion" href="#collapse4">
							Receipt Information (Optional)</a>
					</h4>
				</div>
				<div id="collapse4" class="panel-collapse collapse">
					<div class="panel-body">

						<div class="row">
							<div class="col-md-6">
								<div class="col-md-12">
									<div class="form-group">
										<label>Receipt Note:</label> <span class="text-danger" data-error="receipt_note"></span>
										<textarea class="form-control" name="receipt_note" placeholder="Receipt Note (Optional)"></textarea>
									</div>
									<div class="form-group">
										<label>Receipt Phone:</label> <span class="text-danger" data-error="receipt_phone"></span>
										<div class="input-group">
											<input type="text" name="receipt_phone" class="form-control" placeholder="Receipt Phone (Optional)">
											<span style="cursor: pointer;" class="input-group-addon" title="Click to Get from Outlet" id="getOutletPhone"><i class="fa fa-crosshairs"></i></span>
										</div>
									</div>
									<div class="form-group">
										<label>Receipt Address:</label> <span class="text-danger" data-error="receipt_address"></span>
										<div class="input-group">
											<input type="text" name="receipt_address" class="form-control" placeholder="Receipt Address (Optional)">
											<span style="cursor: pointer;" class="input-group-addon" title="Click to Get from Outlet" id="getOutletAddress"><i class="fa fa-crosshairs"></i></span>
										</div>
									</div>
									<div class="form-group">
										<label>Receipt Social Media:</label> <span class="text-danger" data-error="receipt_socialmedia"></span>
										<textarea name="receipt_socialmedia" class="form-control" placeholder="Receipt Social Media (Optional)"></textarea>
									</div>
								</div>
							</div>
							<div class="col-md-6">
								<div class="col-md-12">
									<div class="form-group">
										<label>Receipt Logo (max. 100KB):</label> <span class="text-danger" data-error="receipt_logo"></span>
										<input type="file" name="receipt_logo">
										<div style="clear: both;"></div>
										<div id="receipt_logo" class="img-wrap"></div>
										<input type="hidden" name="receipt_logo_url">
									</div>
								</div>
							</div>
						</div>

					</div>
				</div>
			</div>
			<!-- panel sharing -->
			<div class="panel panel-default">
				<div class="panel-heading">
					<h4 class="panel-title">
						<a data-toggle="collapse" data-parent="#accordion" href="#collapse5">Commission (Optional)</a>
					</h4>
				</div>
				<div id="collapse5" class="panel-collapse collapse">
					<div class="panel-body">
						<div class="row">
							<div class="col-md-12">
								<div class="form-group">
									<label class="col-md-2 control-label">Sharing Commission:</label>
									<div class="col-md-10">
										<div class="input-group">
											<input type="text" class="form-control" name="commission_sharing" placeholder="Commission on Percentage" min="0" max="100">
											<span class="input-group-addon">%</span>
										</div>
										<span class="text-danger" data-error="commission_sharing"></span>
									</div>
								</div>
								<!-- /.form-group -->
							</div>
						</div>
						<!-- /.row -->
					</div>
					<!-- /.panel-body -->
				</div>
				<!-- /.panel-collapse -->
			</div>
		</div>

		<div class="col-md-12">
			<div style="font-size: small;float: right;font-style: italic;">(*) Must be filled</div>
			<div style="clear: both;"></div>
			<div class="pull-right">
				<button type="button" class="btn btn-default" onclick="showPageIndex()">Back</button>
				<button type="submit" class="btn btn-primary">Save</button>
			</div>
		</div>
	</form>
	<div style="clear: both;"></div>
</div>

<!-- GMAP JS -->
<script>
	let tableTags = null;
	var public_marker;

	function latlongToForm(coor) {
		$('#myform [name=lat]').val(coor.lat());
		$('#myform [name=long]').val(coor.lng());

		latlongToAddress(coor.lat(), coor.lng())
	}

	function latlongToAddress(lat, lng) {
		var geocoder = new google.maps.Geocoder;


		var latlng = new google.maps.LatLng(lat, lng);
		geocoder.geocode({
			'latLng': latlng
		}, function(results, status) {
			if (status === google.maps.GeocoderStatus.OK) {
				if (results[0]) {
					//set to form
					var data = results[0];
					var addr = data.address_components;

					var street_number = "",
						route = "",
						fulladdr = "";

					addr.forEach(function(d) {
						var long_name = d.long_name;
						var type = d.types[0];

						if (long_name != undefined || long_name != "undefined") {
							switch (type) {
								case "postal_code":
									$('#myform [name=postal_code]').val(long_name)
									break;
								case "country":
									$('#myform [name=country]').val(long_name)
									break;
								case "administrative_area_level_1":
									$('#myform [name=province]').val(long_name)
									break;
								case "administrative_area_level_2":
									$('#myform [name=city]').val(long_name)
									break;
								case "route":
									route = long_name;
									break;
								case "street_number":
									street_number = long_name;
									break;
								default:
							}
						}

					})

					//set full address
					fulladdr = route + " " + street_number;
					$('#myform [name=address]').val(fulladdr);
				} else {
					alert('No results found');
				}
			} else {
				alert('Geocoder failed due to: ' + status);
			}
		});
	}

	function addYourLocationButton(map, marker) {
		var controlDiv = document.createElement('div');

		var firstChild = document.createElement('button');
		firstChild.type = 'button';
		firstChild.style.backgroundColor = '#fff';
		firstChild.style.border = 'none';
		firstChild.style.outline = 'none';
		firstChild.style.width = '40px';
		firstChild.style.height = '40px';
		firstChild.style.borderRadius = '2px';
		firstChild.style.boxShadow = '0px 1px 4px -1px rgba(0,0,0,0.3)';
		firstChild.style.cursor = 'pointer';
		firstChild.style.marginRight = '10px';
		firstChild.style.padding = '0px';
		firstChild.title = 'Your Location';
		controlDiv.appendChild(firstChild);

		var secondChild = document.createElement('div');
		secondChild.style.margin = 'auto';
		secondChild.style.width = '18px';
		secondChild.style.height = '18px';
		secondChild.style.backgroundImage = 'url(https://maps.gstatic.com/tactile/mylocation/mylocation-sprite-1x.png)';
		secondChild.style.backgroundSize = '180px 18px';
		secondChild.style.backgroundPosition = '0px 0px';
		secondChild.style.backgroundRepeat = 'no-repeat';
		secondChild.id = 'you_location_img';
		firstChild.appendChild(secondChild);

		google.maps.event.addListener(map, 'dragend', function() {
			$('#you_location_img').css('background-position', '0px 0px');
		});

		firstChild.addEventListener('click', function(e) {
			e.preventDefault(); //prevent submit when click my location

			var imgX = '0';
			var animationInterval = setInterval(function() {
				if (imgX == '-18') imgX = '0';
				else imgX = '-18';
				$('#you_location_img').css('background-position', imgX + 'px 0px');
			}, 500);
			if (navigator.geolocation) {
				navigator.geolocation.getCurrentPosition(function(position) {
					var latlng = new google.maps.LatLng(position.coords.latitude, position.coords.longitude);

					// Clear out the old markers.
					markers.forEach(function(marker) {
						marker.setMap(null);
					});
					markers = [];

					// Create a marker for each place.
					var marker = new google.maps.Marker({
						map: map,
						position: latlng,
						draggable: true,
					});
					marker.setPosition(latlng);
					map.setCenter(latlng);
					markers.push(marker);

					// set marker with drag response for every marker
					markers.forEach(function(marker) {
						google.maps.event.addListener(marker, 'dragend', function(e) {
							latlongToForm(this.getPosition());
							// map.setCenter(this.getPosition()); // Set map center to marker position
						});
					});


					clearInterval(animationInterval);
					$('#you_location_img').css('background-position', '-144px 0px');

					var bounds = new google.maps.LatLngBounds();
					bounds.extend(latlng);
					map.setZoom(15);
					latlongToForm(latlng);
				});
			} else {
				clearInterval(animationInterval);
				$('#you_location_img').css('background-position', '0px 0px');
			}
		});

		controlDiv.index = 1;
		map.controls[google.maps.ControlPosition.RIGHT_BOTTOM].push(controlDiv);
	}

	var map;
	var markers = [];

	function remove_marker() {
		// Clear out the old markers.
		markers.forEach(function(marker) {
			marker.setMap(null);
		});
		markers = [];
	}

	function initMap() {
		//init map
		map = new google.maps.Map(document.getElementById('map'), {
			center: {
				lat: -7.797068,
				lng: 110.370529
			},
			zoom: 10,
			mapTypeControl: false,
			streetViewControl: false,
		});

		/* MY LOCATION */
		addYourLocationButton(map);


		/* SEARCH BOX START */
		// Create the search box and link it to the UI element.
		var input = document.getElementById('pac-input');
		var searchBox = new google.maps.places.SearchBox(input);
		map.controls[google.maps.ControlPosition.TOP_LEFT].push(input);

		// Bias the SearchBox results towards current map's viewport.
		map.addListener('bounds_changed', function() {
			searchBox.setBounds(map.getBounds());
		});

		// var markers = [];
		// Listen for the event fired when the user selects a prediction and retrieve
		// more details for that place.
		searchBox.addListener('places_changed', function() {
			var places = searchBox.getPlaces();

			if (places.length == 0) {
				return;
			}

			// Clear out the old markers.
			markers.forEach(function(marker) {
				marker.setMap(null);
			});
			markers = [];

			// For each place, get the icon, name and location.
			var bounds = new google.maps.LatLngBounds();
			places.forEach(function(place) {
				if (!place.geometry) {
					console.log("Returned place contains no geometry");
					return;
				}
				var icon = {
					url: place.icon,
					size: new google.maps.Size(71, 71),
					origin: new google.maps.Point(0, 0),
					anchor: new google.maps.Point(17, 34),
					scaledSize: new google.maps.Size(25, 25)
				};

				// Create a marker for each place.
				markers.push(new google.maps.Marker({
					map: map,
					// icon: icon,
					// title: place.name,
					position: place.geometry.location,
					draggable: true,
				}));

				/* MY CUSTOM SCRIPT START */
				// set marker with drag response for every marker
				markers.forEach(function(marker) {
					google.maps.event.addListener(marker, 'dragend', function(e) {
						latlongToForm(this.getPosition());
						// map.setCenter(this.getPosition()); // Set map center to marker position
					});
				});

				// set long lat of marker to form
				latlongToForm(place.geometry.location);
				/* MY CUSTOM SCRIPT END */

				if (place.geometry.viewport) {
					// Only geocodes have viewport.
					bounds.union(place.geometry.viewport);
				} else {
					bounds.extend(place.geometry.location);
				}
			});
			map.fitBounds(bounds);
		});

		// Prevent submit on result when select with enter
		google.maps.event.addDomListener(input, 'keydown', function(event) {
			if (event.keyCode === 13) {
				event.preventDefault();
			}
		});
		/* SEARCH BOX END */

		/* FIX SEARCH RESULT DROPDOWN START */
		document.onfullscreenchange = function(event) {
			let target = event.target;
			let pacContainerElements = document.getElementsByClassName("pac-container");
			if (pacContainerElements.length > 0) {
				let pacContainer = document.getElementsByClassName("pac-container")[0];
				if (pacContainer.parentElement === target) {
					// console.log("Exiting FULL SCREEN - moving pacContainer to body");
					document.getElementsByTagName("body")[0].appendChild(pacContainer);
				} else {
					// console.log("Entering FULL SCREEN - moving pacContainer to target element");
					target.appendChild(pacContainer);
				}
			} else {
				// console.log("FULL SCREEN change - no pacContainer found");

			}
		};
		/* FIX SEARCH RESULT DROPDOWN END */
	}
</script>
<script src="https://maps.googleapis.com/maps/api/js?key=<?= $gmap_key ?>&libraries=places&callback=initMap" async defer></script>




<script type="text/javascript">
	//init treegrid: outlet feature
	$('#myform table').treegrid({
		initialState: 'collapsed'
	});

	//reset form
	function resetFormCreate() {
		//cek expand on first panel
		var first_panel = $('#myform #accordion h4:first a');
		var id_first_panel = first_panel.attr('href')
		if (!$(id_first_panel).hasClass('in')) {
			first_panel.click();
		}

		/**
		 * Reset order type table
		 */
		if (tableTags != null) {
			tableTags.clear()
			tableTags.destroy()
		}
		tableTags = $("#table-tags").DataTable({
			searching: false,
			ordering: false,
			info: false,
			lengthChange: false,
			autoWidth: false,
			columns: [{
					data: "name",
					render: function(data, type, row, meta) {
						return `<input data-sales-tag-id="${row.sales_tag_id}" type="text" data-admin-fkid="${row.admin_fkid}" data-outlet-fkid="${row.outlet_fkid}" value="${data ?? ''}" class="form-control" style="width: 100%;" placeholder="Type name" />`
					}
				},
				{
					className: "text-center",
					data: "data_status",
					render: function(data, type, row, meta) {
						return `<input data-sales-tag-id="${row.sales_tag_id}" type="checkbox"/>`
					}
				},
				{
					className: "text-center",
					data: "sales_tag_id",
					render: function(data, type, row, meta) {
						return `<a href="javascript:void(0)"  class="btn btn-xs btn-danger delete-row"><i class="fa fa-trash"></i></a>`
					}
				}
			]
		})

		let indexAdd = 0;
		$("#table-tags").off("click", ".add-type")

		$("#table-tags").on("click", ".add-type", function() {
			tableTags.row.add({
				index: indexAdd,
				sales_tag_id: "",
				name: "",
				admin_fkid: "",
				data_status: "",
				outlet_fkid: "",
			}).draw()

			indexAdd++
		})

		//reset form
		$('#myform [data-error]').html('');
		$('#myform')[0].reset();

		//hide image preview
		$('#myform [name=outlet_logo]').show();
		$('#myform #outlet_logo').hide();
		$('#myform [name=receipt_logo]').show();
		$('#myform #receipt_logo').hide();

		//hide working hour
		$('#myform table[data-workinghour]').hide()
		$('#myform table[data-workinghour] [type=checkbox]').prop('checked', false).change();

		//reset table sosmed
		$('#myform #table-socialmedia > tbody').html('<tr><td colspan="3" class="text-center">No Data</td></tr>');

		//reset marker
		remove_marker();

		//outlet feature
		$('#outlet-feature').treegrid('collapseAll');
		resetOutletFeature()
	}

	function resetOutletFeature() {
		//outlet feature disable sub-feature
		<?php foreach ($featurelist as $k1 => $v1) : ?>
			$('#myform table [data-lv1=<?= $v1[0] ?>]').prop('disabled', true);
		<?php endforeach ?>
	}


	//FORM EVENT
	//get current location
	$('#myform #getPosition').click(function() {
		$('#you_location_img').click()
	})

	//logo preview
	function readURL(input, element) {
		if (input.files && input.files[0]) {
			var reader = new FileReader();

			reader.onload = function(e) {
				// $('#outlet_logo').attr('src', e.target.result);

				$('#' + element).html('\
				<span class="close" onclick="remove_img(\'' + element + '\')">&times;</span> \
				<img src="' + e.target.result + '" style="max-width: 300px"/>\
			').show();

				//set hidden url
				$('#myform [name=' + element + '_url]').val(e.target.result)

				//hide input file
				$('#myform [name=' + element + ']').hide()
			}

			reader.readAsDataURL(input.files[0]);
		}
	}

	function remove_img(element) {
		//init
		var text = '';
		if (element == 'outlet_logo') {
			text = 'Outlet Logo';
		} else if (element == 'receipt_logo') {
			text = 'Receipt Logo';
		}


		//konfirm delete
		var c = confirm('Remove ' + text + '?');
		if (c) {
			$('#myform [name=' + element + '_url]').val(''); //delete hidden image url
			$('#myform #' + element).html('').hide(); //hide image preview
			$('#myform [name=' + element + ']').val('').show(); //reset input value
		}
	}

	$("#myform [name=outlet_logo]").change(function() {
		readURL(this, 'outlet_logo');
	});
	$("#myform [name=receipt_logo]").change(function() {
		readURL(this, 'receipt_logo');
	});

	//working hour
	$('#myform [name=workinghour]').change(function() {
		//hide working hour element
		$('#myform table[data-workinghour]').hide();

		//show element by value
		var value = $(this).val();
		if (value != "") {
			$('#myform table[data-workinghour=' + value + ']').show();
		}
	})
	$('#myform [data-workinghour=alwaysopen] [type=checkbox]').change(function() {
		//ambil day
		var hari = $(this).data('day');
		var element = $('#myform [data-workinghour=alwaysopen] [data-open=' + hari + ']');

		//set text
		if ($(this).prop('checked') == true) {
			element.html('24 hours')
		} else {
			element.html('Closed')
		}
	})
	$('#myform [data-workinghour=selectedhour] [type=checkbox]').change(function() {
		var day = $(this).data('day');
		var is_checked = $(this).prop('checked');

		//get open
		var a = $(this).parent().parent().find('[data-open=' + day + ']')

		if (is_checked == true) {
			var p = a.children('div input').length;

			//reset inputan
			if (p == 0) {
				a.html(' \
				<div> \
					<a href="javascript:void(0)" data-day="' + day + '" onclick="add_set_hour($(this))"> \
						<small><i class="fa fa-plus"></i> Add a Set of Hour</small> \
					</a> \
				</div> \
			');
			}

			add_hour_input(a, day);
		} else {
			a.html('Closed')
		}
	})

	function add_set_hour(ini) {
		var day = ini.data('day');
		var element = ini.parent().parent().parent().find('[data-open=' + day + ']');
		add_hour_input(element, day);
	}

	function add_hour_input(element, day, value = '') {
		var a = element.find('div:last');
		var p = element.find('div input').length;
		var limit = 2; //jumlah input max
		if (p <= (limit - 1)) {
			var CM = currentMillis() + '-' + makeid(10);
			a.before('\
			<div class="input-group" data-id="' + CM + '"> \
				<input type="text" class="form-control" placeholder="Open" value="' + value + '" name="workinghour_selectedhour[' + day + '][]"> \
				<span style="cursor: pointer;" class="input-group-addon" onclick="remove_hour_input($(this))"><i class="fa fa-trash"></i></span> \
			</div>')

			//init daterange on input
			a.parent().find('[data-id=' + CM + '] input').daterangepicker({
				timePicker: true,
				timePicker24Hour: true,
				timePickerIncrement: 1,
				// timePickerSeconds: true,
				locale: {
					format: 'HH:mm'
				}
			}).on('show.daterangepicker', function(ev, picker) {
				picker.container.find(".calendar-table").hide();
			});

			//show set hour
			a.parent().find('div:last').show();
		}

		if (p >= (limit - 1)) {
			//hidden set hour
			a.parent().find('div:last').hide();
		}
	}

	function remove_hour_input(ini) {
		//init
		var element_block = ini.parent().parent();
		// var day = element_block.data('open');

		//hapus element
		var dataid = ini.parent().data('id');
		var element = element_block.find('[data-id=' + dataid + ']');
		var main_element = element.parent();

		//hapus element
		element.remove();

		//cek input length
		var a = main_element.find('div input').length;
		if (a <= 1) {
			//show set hour
			main_element.find('div:last').show();
		}
		if (a <= 0) {
			//centang active
			element_block.next().children('[type=checkbox]').prop('checked', false).change()
		}

	}

	//social media event list
	//add socmed
	$('#table-socialmedia a[data-action=add]').click(function() {
		var element = $('#table-socialmedia tbody');
		var CM = currentMillis() + '-' + makeid(10);

		//cek sosmed
		var l = $('#table-socialmedia tbody input').length;
		if (l <= 0) {
			//reset element
			element.html('')
		}

		//tambah input sosmed
		element.append('\
		<tr>\
		<td><input type="text" name="socialmedia[' + CM + '][name]" class="form-control" placeholder="Custom Social Name" required></td>\
		<td><input type="text" name="socialmedia[' + CM + '][value]" class="form-control" placeholder="Custom Social Address" required></td>\
		<td><a href="javascript:void(0)" onclick="delete_row($(this))" class="btn btn-danger btn-xs"><i class="fa fa-trash"></i></a></td>\
		</tr>');
	})

	//delete socmed
	function delete_row(ini) {
		var row = ini.parent().parent();
		var row_body = row.parent();
		var l = row.parent().children('tr').length;
		row.remove();

		//cek kosong
		if ((l - 1) <= 0) {
			//reset table
			row_body.html('<tr><td colspan="3" class="text-center">No Data</td></tr>');
		}
	}

	//select all outlet feature
	$('#myform [name=all_feature]').change(function() {
		if ($(this).prop('checked') == true) {
			//centang semua outlet feature
			$('#myform table [type=checkbox]').prop('disabled', false)
			$('#myform table [type=checkbox]').prop('checked', true)
		} else {
			resetOutletFeature()
			$('#myform table [type=checkbox]').prop('checked', false)
		}
	})

	//klik outlet feature yang punya sub-feature
	$('#myform table input[type=checkbox]').change(function() {
		var b = $(this).prop('name');
		b = b.replace("feature[", "").replace("]", "");



		//event yang ada subnya
		<?php foreach ($featurelist as $k1 => $v1) : ?>
			<?php if (!empty($featurelist2[$v1[0]])) : ?>
				if (b == "<?= $v1[0] ?>") {
					//check element sekarang aktif atau tidak
					if ($(this).prop('checked') == true) {
						$('#myform table [type=checkbox][data-lv1=<?= $v1[0] ?>]').prop('checked', true).prop('disabled', false)
					} else {
						$('#myform table [type=checkbox][data-lv1=<?= $v1[0] ?>]').prop('checked', false).prop('disabled', true)
					}
				}
			<?php endif ?>
		<?php endforeach ?>
	})





	// receipt
	// receipt get phone
	$('#getOutletPhone').click(function() {
		var v = $('#myform [name=phone]').val();
		$('#myform [name=receipt_phone]').val(v)
	})

	// receipt get address
	$('#getOutletAddress').click(function() {
		var v = $('#myform [name=address]').val();
		$('#myform [name=receipt_address]').val(v)
	})

	//receipt social media
	$('#myform [name=receipt_socialmedia]').keydown(function(e) {
		if (e.keyCode == 13) {
			e.preventDefault();
		}
	})

	//form submit
	$('#myform').submit(function(e) {
		e.preventDefault();
		var action = $('#myform [name=action]').val();
		var form_url = (action == 'create') ? '<?= $ajaxActionCreate ?>' : '<?= $ajaxActionUpdate ?>';

		let inputType = tableTags.$("input")
		let inputGroup = []

		if (inputType.length > 0) {
			for (let i = 0; i < inputType.length; i += 2) {
				inputGroup.push({
					sales_tag_id: $(inputType[i]).data("sales-tag-id"),
					admin_fkid: $(inputType[i]).data("admin-fkid"),
					outlet_fkid: $(inputType[i]).data("outlet-fkid"),
					name: inputType[i].value,
					data_status: $(inputType[i + 1]).is(":checked") == true ? "on" : "off",
				})
			}
		}

		let formData = new FormData(this)
		if (inputGroup.length > 0) {
			formData.append("order_type", JSON.stringify(inputGroup))
		}

		$.ajax({
			type: 'post',
			url: form_url,
			// data: $(this).serialize(),
			data: formData,
			processData: false,
			contentType: false,
			dataType: 'json',
			beforeSend: function() {
				loading_show();
			},
			success: function(response) {
				loading_hide()

				if (response.status == 'success') {
					Swal('Success!', response.message, 'success');

					//reload datatables
					$('#mytable').DataTable().ajax.reload(null, false);

					//reset form
					if (action == 'create') {
						resetFormCreate();
					}

					//reset error
					$('#myform [data-error]').html('');
				} else {
					Swal('Oops...', response.message, 'error');

					//set error
					$('#myform .text-danger').html(''); //reset form error
					$.each(response.data, function(k, v) {
						$('#myform [data-error=' + k + ']').html(v);
					})
				}
			},
			error: function() {
				loading_hide();
				Swal('Oops...', 'Something went wrong!', 'error');
			}
		})
	})

	$(document).ready(function() {
		// Initialize tooltips
		$('[data-toggle="tooltip"]').tooltip({
			html: true,
			container: 'body'
		});
	});
</script>