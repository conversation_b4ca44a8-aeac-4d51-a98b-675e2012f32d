<div class="content-uniq">
    <table class="table table-responsive table-report" id="mytable" width="100%">
        <thead>
            <tr>
                <th width="80px">No</th>
                <th>Name</th>
                <th>Address</th>
                <th>Phone</th>
                <th>Country</th>
                <th>Province</th>
                <th>City</th>
                <th>Postal Code</th>
                <th width="80px">#</th>
            </tr>
        </thead>
    </table>
</div>



<!-- DATATABLE LOAD START -->
<script type="text/javascript">
$(document).ready(function() {
    $.fn.serializeObject = function(){
        var obj = {};

        $.each( this.serializeArray(), function(i,o){
            var n = o.name, v = o.value;
            obj[n] = obj[n] === undefined ? v
            : $.isArray( obj[n] ) ? obj[n].concat( v )
            : [ obj[n], v ];
        });
        return obj;
    };

    $.fn.dataTableExt.oApi.fnPagingInfo = function(oSettings)
    {
        return {
            "iStart": oSettings._iDisplayStart,
            "iEnd": oSettings.fnDisplayEnd(),
            "iLength": oSettings._iDisplayLength,
            "iTotal": oSettings.fnRecordsTotal(),
            "iFilteredTotal": oSettings.fnRecordsDisplay(),
            "iPage": Math.ceil(oSettings._iDisplayStart / oSettings._iDisplayLength),
            "iTotalPages": Math.ceil(oSettings.fnRecordsDisplay() / oSettings._iDisplayLength)
        };
    };

    var table = 'mytable';
    var t = $("#"+table).DataTable({
        dom: '<"pull-left"l><"toolbar">frtip', //buat custom toolbar
        initComplete: function() {
            var api = this.api();
            $('#'+table+'_filter input').off('.DT').on('keyup.DT', function(e) {
                if (e.keyCode == 13) {
                    api.search(this.value).draw();
                }
            });

            //tombol add
            $("#"+table+"_wrapper > div.toolbar").html(''+
                '<div class="btn-group pull-right">'+
                    '<button type="button" class="btn btn-info uniq-add" onclick="showPageCreate()" style="margin-left:5px; padding:4px 10px;"><span class="fa fa-plus"></span> Add New</button>'+
                    '<button type="button" class="btn btn-info" onclick="showModalEditBulk()" style="margin-left:5px; padding:4px 10px;"><span class="fa fa-edit"></span> Bulk Update</button>'+
                '</div>');
        },
        oLanguage: {
            sProcessing: loading_datatable()
        },
        responsive: true,
        processing: true,
        serverSide: true,
        ajax: {
            url: "<?=$ajaxActionDatatableList ?>",
            type: "POST",
            beforeSend: function(xhr) {
                xhr.setRequestHeader('Authorization', getUniqToken());
            },
            dataSrc: function(response) {
                this.tryCount = 0;
                return response.data;
            },
            error: function(xhr, status, error) {
                if (xhr.status == 401) {
                    return jqRefreshToken(this, xhr);
                }
                alert(error)
            },
        },
        columns: [
            {data: "id"},
            {
                data: "name",
                render: $.fn.dataTable.render.text()
            },
            {
                data: "address",
                render: $.fn.dataTable.render.text()
            },
            {
                data: "phone",
                render: $.fn.dataTable.render.text()
            },
            {
                data: "country",
                render: $.fn.dataTable.render.text()
            },
            {
                data: "province",
                render: $.fn.dataTable.render.text()
            },
            {
                data: "city",
                render: $.fn.dataTable.render.text()
            },
            {
                data: "postal_code",
                render: $.fn.dataTable.render.text()
            },
            {
                orderable: false,
                searchable: false,
                className : "text-center",
                render:function(data,type,row,meta){
                    var btn_edit = '<button class="btn btn-warning btn-xs" onclick="tombolEdit('+row.id+')"><i class="fa fa-edit"></i></button>';
                    var btn_delete = '<button class="btn btn-danger btn-xs" onclick="tombolDelete('+row.id+')"><i class="fa fa-trash"></i></button>';
                    return btn_edit + ' ' +btn_delete;
                }
            }
        ],
        order: [[1, 'asc']],
        rowCallback: function(row, data, iDisplayIndex) {
            var info = this.fnPagingInfo();
            var page = info.iPage;
            var length = info.iLength;
            var index = page * length + (iDisplayIndex + 1);
            $('td:eq(0)', row).html(index);
        }
    });
});
</script>
<!-- DATATABLE LOAD END -->