<style type="text/css">
	[data-page] {
		display: none;
	}

	/* treegrid css */
	table.mytreegrid thead tr th:nth-child(2),
	table.mytreegrid tbody tr td:nth-child(2) {
		text-align: center;
	}

	/* daterangepicker (just get time) */
	.daterangepicker select.hourselect,
	.daterangepicker select.minuteselect,
	.daterangepicker select.secondselect,
	.daterangepicker select.ampmselect {
		color: initial;
	}
</style>


<div class="container-fluid" data-page="page-index">
	<?php $this->load->view('outlet_datatable_v'); ?>
</div><!-- /.container-fluid -->

<div class="container-fluid" data-page="page-create">
	<?php $this->load->view('outlet_form_v'); ?>
</div><!-- /.container-fluid -->

<!-- modal -->
<div id="ajax-content"></div>


<!-- GENERAL JS -->
<script type="text/javascript">
	function makeid(length) {
		var result = '';
		var characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
		var charactersLength = characters.length;
		for (var i = 0; i < length; i++) {
			result += characters.charAt(Math.floor(Math.random() * charactersLength));
		}
		return result;
	}
</script>


<script type="text/javascript">
	//page event
	function hideAllPage() {
		$('[data-page^="page-"]').hide();
	}

	function showPageIndex() {
		document.title = 'Outlet List';
		hideAllPage();
		$('[data-page="page-index"]').slideDown(100);
	}

	function showPageCreate() {
		<?php if ($permission['add']) : ?>
			document.title = 'Create Outlet';
			hideAllPage();
			$('[data-page="page-create"]').slideDown(100);

			//function on create_v
			resetFormCreate();
			$('#myform [name=action]').val('create');

		<?php else : ?>
			Swal('Access Forbidden!', 'Please contact your administrator.', 'error');
		<?php endif ?>
	}

	function showPageEdit(outlet_name = '') {
		// resetFormCreate();
		$('#myform [name=action]').val('edit');

		document.title = 'Update Outlet ' + outlet_name;
		hideAllPage();
		$('[data-page="page-create"]').slideDown(100);
	}

	function showModalEditBulk() {
		let modal_url = '<?=current_url().'/modal_bulkupdate' ?>';
		$.ajax({
			url: modal_url,
			beforeSend: function() {
				loading_show();
			},
			success: function(response, status, xhr) {
				$('#ajax-content').html(response);
				$('#modal-form-bulkupdate').modal('show');
			},
			error: function(xhr, status, message) {
				let response_status = xhr.status;
				let response = xhr.responseText;
				let rJSON = xhr.responseJSON;
				if (rJSON != undefined) {
					Swal.fire('Oops..!', rJSON.message, 'error');
					return;
				}

				Swal.fire('Oops..!', `[${response_status}] ${message}`, status);
			}
		}).always(function() {
			loading_hide();
		});
	}


	$(document).ready(function() {
		hideAllPage();
		$('[data-page="page-index"]').show(); //show index page
	})


	//datatable event
	function tombolEdit(id) {
		<?php if ($permission['edit']) : ?>
			$.ajax({
				type: 'get',
				url: "<?= $ajaxActionEdit ?>" + id,
				dataType: 'json',
				beforeSend: function() {
					loading_show()
				},
				success: function(response) {
					loading_hide()


					//response action
					if (response.status == 'success') {
						resetFormCreate();

						var data = response.data;
						$('#myform [name=action]').val('update');

						$.each(data, function(k, v) {
							switch (k) {
								case 'outlet_logo':
									if (v != "") {
										//show image preview
										$('#myform #' + k).html('\
									<span class="close" onclick="remove_img(\'' + k + '\')">&times;</span> \
									<img src="' + v + '" style="max-width: 300px"/>\
								').show();

										//set value image_url
										$('#myform [name=' + k + '_url]').val(v);

										//hide input file
										$('#myform [name=' + k + ']').hide()
									}
									break;
								case 'location':
								case 'receipt':
									$.each(this, function(k2, v2) {
										if (k2 == 'receipt_logo' && v2 != "") {
											//show image preview
											$('#myform #' + k2).html('\
										<span class="close" onclick="remove_img(\'' + k2 + '\')">&times;</span> \
										<img src="' + v2 + '" style="max-width: 300px"/>\
									').show();

											//set value image_url
											$('#myform [name=' + k2 + '_url]').val(v2);

											//hide input file
											$('#myform [name=' + k2 + ']').hide()
										} else {
											$('#myform [name=' + k2 + ']').val(v2);
										}
									})

									//location
									if (k == 'location') {
										remove_marker();

										var latlng = JSON.parse('{"lat": ' + this.lat + ', "lng": ' + this.long + '}');
										// var latlng = '{'+this.lat+ ','+ this.long +'}';
										// set marker with drag response for every marker
										// Create a marker for each place.
										var marker = new google.maps.Marker({
											map: map,
											position: latlng,
											draggable: true,
										});
										marker.setPosition(latlng);
										map.setCenter(latlng);
										markers.push(marker);

										// set marker with drag response for every marker
										markers.forEach(function(marker) {
											google.maps.event.addListener(marker, 'dragend', function(e) {
												latlongToForm(this.getPosition());
												// map.setCenter(this.getPosition()); // Set map center to marker position
											});
										});
									}
									break;
								case 'workinghour':
									$('#myform [name=' + k + ']').val(v);

									switch (v) {
										case 'alwaysopen':
											$('#myform [data-workinghour=' + v + ']').show();
											$.each(data.workinghour_list, function(k3, v3) {
												$('#myform [data-workinghour=' + v + '] [data-day=' + k3 + ']').prop('checked', true).change();
											})
											break;
										case 'selectedhour':
											$('#myform [data-workinghour=' + v + ']').show();
											$.each(data.workinghour_list, function(k3, v3) {
												var day = k3;

												//aktifkan ceklist
												$('#myform [data-workinghour=' + v + '] [data-day=' + k3 + ']').prop('checked', true).change();

												//reset inputan html
												var element = $('#myform [data-workinghour=' + v + '] [data-open=' + k3 + ']');
												element.html('\
											<div> \
												<a href="javascript:void(0)" data-day="' + day + '" onclick="add_set_hour($(this))"> \
													<small><i class="fa fa-plus"></i> Add a Set of Hour</small> \
												</a> \
											</div> \
										');

												//set time to input
												$.each(this, function(k4, v4) {
													var CM = currentMillis();
													var value = v4.time_open + ' - ' + v4.time_close;

													add_hour_input(element, day, value);
												});
											});

											break;
										default:
											$('#myform [name=' + k + ']').val("");
											break;
									}
									break;
								case 'feature':
									$.each(this, function(k2, v2) {
										if (typeof(v2) === 'object') {
											$.each(this, function(k3, v3) {
												if (v3 == true) {
													$('#myform .treegrid-' + k2 + ' [type=checkbox]').prop('checked', true);
													$('#myform [data-lv1=' + k2 + ']').prop('disabled', false);
													$('#myform [data-lv1=' + k2 + '][data-lv2=' + k3 + '][type=checkbox]').prop('checked', true);
												}
											});
										} else {
											if (v2 == true) {
												$('#myform .treegrid-' + k2 + ' [type=checkbox]').prop('checked', true);
											}
										}
									});
									break;
								case 'socialmedia':
									$.each(this, function(k2, v2) {
										if (k2 == 'other') {
											$.each(this, function(k3, v3) {
												var CM = currentMillis() + '-' + makeid(10);

												//cek jumlah input
												var l = $('#myform #table-socialmedia > tbody input[type=text]').length;
												if (l == 0) {
													$('#myform #table-socialmedia > tbody').html('');
												}

												//append
												$('#myform #table-socialmedia > tbody').append('' +
													'<tr> \
											<td><input type="text" name="socialmedia[' + CM + '][name]" value="' + k3 + '" class="form-control" placeholder="Custom Social Name" required></td> \
											<td><input type="text" name="socialmedia[' + CM + '][value]" value="' + v3 + '" class="form-control" placeholder="Custom Social Address" required></td> \
											<td><a href="javascript:void(0)" onclick="delete_row($(this))" class="btn btn-danger btn-xs"><i class="fa fa-trash"></i></a></td> \
											</tr> \
										');
											});
										} else {
											$('#myform [data-socialmedia=' + k2 + ']').val(v2);
										}
									});
									break;
								case "sales_outlet_tags":
									if (tableTags != null) {
										tableTags.clear()
										tableTags.destroy()
									}

									tableTags = $("#table-tags").DataTable({
										searching: false,
										ordering: false,
										info: false,
										lengthChange: false,
										autoWidth: false,
										columns: [{
												data: "name",
												render: function(data, type, row, meta) {
													return `<input data-sales-tag-id="${row.sales_tag_id}" type="text" data-admin-fkid="${row.admin_fkid}" data-outlet-fkid="${row.outlet_fkid}" value="${data ?? ''}" class="form-control" style="width: 100%;" placeholder="Type name" />`
												}
											},
											{
												className: "text-center",
												data: "data_status",
												render: function(data, type, row, meta) {
													return `<input data-sales-tag-id="${row.sales_tag_id}" type="checkbox"/>`
												}
											},
											{
												className: "text-center",
												data: "sales_tag_id",
												render: function(data, type, row, meta) {
													return `<a href="javascript:void(0)"  class="btn btn-xs btn-danger delete-row"><i class="fa fa-trash"></i></a>`
												}
											}
										]
									})

									let dataCopy = []
									dataCopy = [...this]
									let dataD = [];
									let mockDt = {};

									dataCopy.forEach((dt, i) => {
										if (i == 0) {
											mockDt.sales_tag_id = ""
											mockDt.name = ""
											mockDt.admin_fkid = dt.admin_fkid
											mockDt.data_status = ""
											mockDt.outlet_fkid = dt.outlet_fkid
										}
										dataD.push({
											index: i,
											sales_tag_id: dt.sales_tag_id,
											name: dt.name,
											admin_fkid: dt.admin_fkid,
											data_status: dt.data_status,
											outlet_fkid: dt.outlet_fkid,
										})
									})

									tableTags.rows.add(dataD).draw()

									let inputType = tableTags.$("input")
									for (let i = 0; i < inputType.length; i += 2) {
										let element = inputType[i + 1]
										let salesTagId = $(element).data("sales-tag-id")
										dataD.forEach((dt) => {
											if (dt.sales_tag_id == salesTagId) {
												if (dt.data_status == "on") {
													$(element).prop('checked', true)
												}
											}
										})
									}

									$("#table-tags").off("click", ".add-type")

									$("#table-tags").on("click", ".add-type", function() {
										tableTags.row.add({
											...mockDt,
											index: dataD.length
										}).draw()
									})

									$("#table-tags").on("click", ".delete-row", function() {
										let data = tableTags.row($(this).parents('tr')).data()
										if (data.sales_tag_id != "") {
											fetch(`<?= $ajaxDeleteSalesTag ?>${data.sales_tag_id}`, {
												method: "DELETE",
											}).then((res) => res.json()).then((data) => {
												if (data.status == 200) {
													tableTags.row($(this).parents('tr')).remove().draw()
												}
											})
										} else {
											tableTags.row($(this).parents('tr')).remove().draw()
										}
									})

									break;
								default:
									$('#myform [name=' + k + ']').val(v);
									break;
							}

						})

						//show page edit
						showPageEdit(data.name);
					} else {
						Swal('Oops...', response.message, 'error');
					}
				},
				error: function() {
					loading_hide();
					Swal('Oops...', 'Something went wrong!', 'error');
				}
			});

		<?php else : ?>
			Swal('Access Forbidden!', 'Please contact your administrator.', 'error');
		<?php endif ?>
	}
	<?php if (ENVIRONMENT == 'development'): ?>
	function tombolDelete_v1(id) {
		<?php if ($permission['delete']) : ?>
			var delete_url = "<?= $ajaxActionDelete ?>" + id;

			Swal.fire({
				title: 'Are you sure?',
				text: "You won't be able to revert this!",
				type: 'warning',
				showCancelButton: true,
				focusCancel: true,
				cancelButtonColor: '#d33',
				confirmButtonColor: '#3085d6',
				confirmButtonText: 'Yes, delete it!'
			}).then((result) => {
				if (result.value) {
					$.ajax({
						type: "DELETE",
						url: delete_url,
						dataType: 'json',
						beforeSend: function() {
							loading_show();
						},
						success: function(response) {
							loading_hide();
							if (response.status == 'success') {
								//data berhasil dihapus
								Swal('Success!', response.message, 'success');
								$('#mytable').DataTable().ajax.reload(null, false); //reload datatables
							} else if (response.status == 'warning') {
								Swal.fire({
									title: response.message,
									text: 'Delete Permanently?',
									type: 'warning',
									showCancelButton: true,
									focusCancel: true,
									cancelButtonColor: '#d33',
									confirmButtonColor: '#3085d6',
									confirmButtonText: 'Yes, delete it permanently!'
								}).then((result) => {
									//delete permanent
									if (result.value) {
										$.ajax({
											type: "DELETE",
											url: delete_url + '/permanent',
											dataType: 'json',
											beforeSend: function() {
												loading_show();
											},
											success: function(response) {
												loading_hide();
												Swal('Success!', response.message, 'success');
												$('#mytable').DataTable().ajax.reload(null, false); //reload datatables
											},
											error: function() {
												loading_hide();
												Swal('Oops...', 'Something went wrong!', 'error');
											}
										})
									}
								})
							} else {
								//data gagal hapus
								Swal('Failed!', response.message, 'error');
							}
						},
						error: function() {
							loading_hide();
							Swal('Oops...', 'Something went wrong!', 'error');
						}
					});
				}
			})

		<?php else : ?>
			Swal('Access Forbidden!', 'Please contact your administrator.', 'error');
		<?php endif ?>
	}
	function tombolEdit_v2(id) {
		<?php if ($permission['edit']): ?>
		loading_show();
		$.ajax({
			type: 'get',
			url: '<?= $url_api ?>' + id,
			dataType: 'json',
			beforeSend: function(xhr) {
				xhr.setRequestHeader('Authorization', getUniqToken());
			},
			success: function(response, status, xhr) {
				resetFormCreate();

				var data = response.data;
				$('#myform [name=action]').val('update');

				$.each(data, function(k, v) {
					switch (k) {
						case 'outlet_logo':
							if (v != "") {
								//show image preview
								$('#myform #' + k).html('\
							<span class="close" onclick="remove_img(\'' + k + '\')">&times;</span> \
							<img src="' + v + '" style="max-width: 300px"/>\
						').show();

								//set value image_url
								$('#myform [name=' + k + '_url]').val(v);

								//hide input file
								$('#myform [name=' + k + ']').hide()
							}
							break;
						case 'location':
						case 'receipt':
							$.each(this, function(k2, v2) {
								if (k2 == 'receipt_logo' && v2 != "") {
									//show image preview
									$('#myform #' + k2).html('\
								<span class="close" onclick="remove_img(\'' + k2 + '\')">&times;</span> \
								<img src="' + v2 + '" style="max-width: 300px"/>\
							').show();

									//set value image_url
									$('#myform [name=' + k2 + '_url]').val(v2);

									//hide input file
									$('#myform [name=' + k2 + ']').hide()
								} else {
									$('#myform [name=' + k2 + ']').val(v2);
								}
							})

							//location
							if (k == 'location') {
								remove_marker();

								var latlng = JSON.parse('{"lat": ' + this.lat + ', "lng": ' + this.long + '}');
								// var latlng = '{'+this.lat+ ','+ this.long +'}';
								// set marker with drag response for every marker
								// Create a marker for each place.
								var marker = new google.maps.Marker({
									map: map,
									position: latlng,
									draggable: true,
								});
								marker.setPosition(latlng);
								map.setCenter(latlng);
								markers.push(marker);

								// set marker with drag response for every marker
								markers.forEach(function(marker) {
									google.maps.event.addListener(marker, 'dragend', function(e) {
										latlongToForm(this.getPosition());
										// map.setCenter(this.getPosition()); // Set map center to marker position
									});
								});
							}
							break;
						case 'workinghour':
							$('#myform [name=' + k + ']').val(v);

							switch (v) {
								case 'alwaysopen':
									$('#myform [data-workinghour=' + v + ']').show();
									$.each(data.workinghour_list, function(k3, v3) {
										$('#myform [data-workinghour=' + v + '] [data-day=' + k3 + ']').prop('checked', true).change();
									})
									break;
								case 'selectedhour':
									$('#myform [data-workinghour=' + v + ']').show();
									$.each(data.workinghour_list, function(k3, v3) {
										var day = k3;

										//aktifkan ceklist
										$('#myform [data-workinghour=' + v + '] [data-day=' + k3 + ']').prop('checked', true).change();

										//reset inputan html
										var element = $('#myform [data-workinghour=' + v + '] [data-open=' + k3 + ']');
										element.html('\
									<div> \
										<a href="javascript:void(0)" data-day="' + day + '" onclick="add_set_hour($(this))"> \
											<small><i class="fa fa-plus"></i> Add a Set of Hour</small> \
										</a> \
									</div> \
								');

										//set time to input
										$.each(this, function(k4, v4) {
											var CM = currentMillis();
											var value = v4.time_open + ' - ' + v4.time_close;

											add_hour_input(element, day, value);
										});
									});

									break;
								default:
									$('#myform [name=' + k + ']').val("");
									break;
							}
							break;
						case 'feature':
							$.each(this, function(k2, v2) {
								if (typeof(v2) === 'object') {
									$.each(this, function(k3, v3) {
										if (v3 == true) {
											$('#myform .treegrid-' + k2 + ' [type=checkbox]').prop('checked', true);
											$('#myform [data-lv1=' + k2 + ']').prop('disabled', false);
											$('#myform [data-lv1=' + k2 + '][data-lv2=' + k3 + '][type=checkbox]').prop('checked', true);
										}
									});
								} else {
									if (v2 == true) {
										$('#myform .treegrid-' + k2 + ' [type=checkbox]').prop('checked', true);
									}
								}
							});
							break;
						case 'socialmedia':
							$.each(this, function(k2, v2) {
								if (k2 == 'other') {
									$.each(this, function(k3, v3) {
										var CM = currentMillis() + '-' + makeid(10);

										//cek jumlah input
										var l = $('#myform #table-socialmedia > tbody input[type=text]').length;
										if (l == 0) {
											$('#myform #table-socialmedia > tbody').html('');
										}

										//append
										$('#myform #table-socialmedia > tbody').append('' +
											'<tr> \
									<td><input type="text" name="socialmedia[' + CM + '][name]" value="' + k3 + '" class="form-control" placeholder="Custom Social Name" required></td> \
									<td><input type="text" name="socialmedia[' + CM + '][value]" value="' + v3 + '" class="form-control" placeholder="Custom Social Address" required></td> \
									<td><a href="javascript:void(0)" onclick="delete_row($(this))" class="btn btn-danger btn-xs"><i class="fa fa-trash"></i></a></td> \
									</tr> \
								');
									});
								} else {
									$('#myform [data-socialmedia=' + k2 + ']').val(v2);
								}
							});
							break;
						case "sales_outlet_tags":
							if (tableTags != null) {
								tableTags.clear()
								tableTags.destroy()
							}

							tableTags = $("#table-tags").DataTable({
								searching: false,
								ordering: false,
								info: false,
								lengthChange: false,
								autoWidth: false,
								columns: [{
										data: "name",
										render: function(data, type, row, meta) {
											return `<input data-sales-tag-id="${row.sales_tag_id}" type="text" data-admin-fkid="${row.admin_fkid}" data-outlet-fkid="${row.outlet_fkid}" value="${data ?? ''}" class="form-control" style="width: 100%;" placeholder="Type name" />`
										}
									},
									{
										className: "text-center",
										data: "data_status",
										render: function(data, type, row, meta) {
											return `<input data-sales-tag-id="${row.sales_tag_id}" type="checkbox"/>`
										}
									},
									{
										className: "text-center",
										data: "sales_tag_id",
										render: function(data, type, row, meta) {
											return `<a href="javascript:void(0)"  class="btn btn-xs btn-danger delete-row"><i class="fa fa-trash"></i></a>`
										}
									}
								]
							})

							let dataCopy = []
							dataCopy = [...this]
							let dataD = [];
							let mockDt = {};

							dataCopy.forEach((dt, i) => {
								if (i == 0) {
									mockDt.sales_tag_id = ""
									mockDt.name = ""
									mockDt.admin_fkid = dt.admin_fkid
									mockDt.data_status = ""
									mockDt.outlet_fkid = dt.outlet_fkid
								}
								dataD.push({
									index: i,
									sales_tag_id: dt.sales_tag_id,
									name: dt.name,
									admin_fkid: dt.admin_fkid,
									data_status: dt.data_status,
									outlet_fkid: dt.outlet_fkid,
								})
							})

							tableTags.rows.add(dataD).draw()

							let inputType = tableTags.$("input")
							for (let i = 0; i < inputType.length; i += 2) {
								let element = inputType[i + 1]
								let salesTagId = $(element).data("sales-tag-id")
								dataD.forEach((dt) => {
									if (dt.sales_tag_id == salesTagId) {
										if (dt.data_status == "on") {
											$(element).prop('checked', true)
										}
									}
								})
							}

							$("#table-tags").off("click", ".add-type")

							$("#table-tags").on("click", ".add-type", function() {
								tableTags.row.add({
									...mockDt,
									index: dataD.length
								}).draw()
							})

							$("#table-tags").on("click", ".delete-row", function() {
								let data = tableTags.row($(this).parents('tr')).data()
								if (data.sales_tag_id != "") {
									fetch(`<?= $ajaxDeleteSalesTag ?>${data.sales_tag_id}`, {
										method: "DELETE",
									}).then((res) => res.json()).then((data) => {
										if (data.status == 200) {
											tableTags.row($(this).parents('tr')).remove().draw()
										}
									})
								} else {
									tableTags.row($(this).parents('tr')).remove().draw()
								}
							})

							break;
						default:
							$('#myform [name=' + k + ']').val(v);
							break;
					}

				})

				//show page edit
				showPageEdit(data.name);
			},
			error: function(xhr, status, error) {
				let response = xhr.responseJSON;
				let response_status = xhr.status;

				//handle server error
				if (response_status >= 500) {
					Swal.fire(error+' ['+response_status+']', 'Please try again or contact our support!', 'error');
					return;
				}

				//handle server invalid response
				if (response == undefined) {
					Swal.fire('Oops..!', 'Server Error ['+xhr.status+']', 'error');
					return;
				}

				//handle error response
				Swal.fire('Oops..!', response.message, 'error');
			}
		}).always(function() {
			loading_hide();
		})
		<?php else: ?>
		Swal('Access Forbidden!', 'Please contact your administrator.', 'error');
		<?php endif ?>
	}
	<?php endif ?>

	function tombolDelete(id) {
		<?php if ($permission['delete']) : ?>
		let url = '<?= $url_api ?>' + id;
		Swal.fire({
			title: 'Are you sure?',
			text: "You won't be able to revert this!",
			type: 'warning',
			showCancelButton: true,
			focusCancel: true,
			cancelButtonColor: '#d33',
			confirmButtonColor: '#3085d6',
			confirmButtonText: 'Yes, delete it!',
			showLoaderOnConfirm: true,
			allowOutsideClick: () => !Swal.isLoading(),
			preConfirm: function() {
				$.ajax({
					type: "DELETE",
					url: url,
					dataType: 'json',
					beforeSend: function(xhr) {
						xhr.setRequestHeader('Authorization', getUniqToken());
					},
					success: function(response, status, xhr) {
						Swal('Success!', response.message, 'success');
						$('#mytable').DataTable().ajax.reload(null, false); //reload datatables
					},
					error: function(xhr, status, error) {
						let response = xhr.responseJSON;
						let response_status = xhr.status;

						//handle server error
						if (response_status >= 500) {
							Swal.fire(error+' ['+response_status+']', 'Please try again or contact our support!', 'error');
							return;
						}

						//handle server invalid response
						if (response == undefined) {
							Swal.fire('Oops..!', 'Server Error ['+xhr.status+']', 'error');
							return;
						}

						//handle error response
						Swal.fire('Oops..!', response.message, 'error');
					}
				});
			}
		})
		<?php else: ?>
		Swal('Access Forbidden!', 'Please contact your administrator.', 'error');
		<?php endif ?>
	}
</script>