<?php
defined('BASEPATH') OR exit('No direct script access allowed');

# v1
// $route['outlets/outletlist']['GET']										= 'outlet_list/Outletlist/index';
$route['outlets/outletlist/datatables']['POST']							= 'outlet_list/Outletlist/datatables';
$route['outlets/outletlist/create']['POST']								= 'outlet_list/Outletlist/create';
$route['outlets/outletlist/get/(:any)']['GET']							= 'outlet_list/Outletlist/get/$1';
$route['outlets/outletlist/update']['POST']								= 'outlet_list/Outletlist/update';
$route['outlets/outletlist/delete/(:any)']['DELETE']					= 'outlet_list/Outletlist/delete/$1';
$route['outlets/outletlist/delete/(:any)/(:any)']['DELETE']				= 'outlet_list/Outletlist/delete/$1/$2';
$route['outlets/outletlist/delete_sales_tag_outlet/(:any)']['DELETE']	= 'outlet_list/Outletlist/delete_sales_tag_outlet/$1';

$route['outlets/outletlist/modal_bulkupdate']['GET']					= 'outlet_list/Outlet_bulkupdate/index_modal';
$route['outlets/outletlist/update_batch']['POST']						= 'outlet_list/Outlet_bulkupdate/update';


if (ENVIRONMENT != 'production') {
	$route['outlets/outletlist_v1']['GET']								= 'outlet_list/Outletlist/index';

	$route['outlets/outletlist_v2']['GET']								= 'outlet_list/Outletlist_v2/index';
	$route['outlets/outletlist_v2/modal_bulkupdate']['GET']				= 'outlet_list/Outletlist_v2/modal_bulkupdate';
}

$route['outlets/outletlist']['GET']										= 'outlet_list/Outletlist_v2/index';

$route['api/outlet-feature']['GET']										= 'outlet_list/Public_data/index';

/* End of file routes.php */
/* Location: ./application/modules/outlet_list/config/routes.php */