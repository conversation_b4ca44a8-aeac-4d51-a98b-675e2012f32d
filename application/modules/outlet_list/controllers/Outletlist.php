<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Outletlist extends Auth_Controller
{

	protected $daylist;
	protected $sosmedlist;
	protected $upload_path = APPPATH . 'cache/uploads/';
	protected $outlet_img_oldpath;
	protected $url_path = 'outlets/outletlist';

	public function __construct()
	{
		parent::__construct();
		$this->load->model('Outlet_model');
		$this->load->library('google/Google_storage');

		//init
		$this->outlet_img_oldpath = FCPATH . 'assets/images/outlets/' . $this->session->userdata('admin_id') . '/';

		//list hari
		$this->daylist = [
			'sunday' => 'Sunday',
			'monday' => 'Monday',
			'tuesday' => 'Tuesday',
			'wednesday' => 'Wednesday',
			'thursday' => 'Thursday',
			'friday' => 'Friday',
			'saturday' => 'Saturday'
		];

		//list sosmed
		$this->sosmedlist = [
			'facebook' => 'Facebook',
			'instagram' => 'Instagram',
			'line' => 'Line',
			'twitter' => 'Twitter',
			'other' => 'Other'
		];
	}

	public function index()
	{
		$data = array(
			'ajaxActionDatatableList' => $this->api.'/outlets/v1/outlet/datatable', //current_url() . '/datatables',
			'ajaxActionCreate' => current_url() . '/create',
			'ajaxActionEdit' => current_url() . '/get/',
			'ajaxActionUpdate' => current_url() . '/update',
			'ajaxActionDelete' => current_url() . '/delete/',
			"ajaxDeleteSalesTag" => current_url() . "/delete_sales_tag_outlet/",

			//ajax modal
			'ajaxview_form_create' => current_url() . '/view_form_add',
		);
		$data['url_api'] = $this->api.'/outlets/v1/outlet/';

		//feature access / permission access
		$data['permission'] = array(
			'add' => $this->privilege->check(uri_string(), 'add'),
			'edit' => $this->privilege->check(uri_string(), 'edit'),
			'delete' => $this->privilege->check(uri_string(), 'delete'),
		);

		$data['daylist'] = $this->daylist;
		$data['sosmedlist'] = $this->sosmedlist;

		//feature
		$data['featurelist'] = $this->outlet_feature->featurelist('mainfeature');
		$data['featurelist2']['authorization'] = $this->outlet_feature->featurelist('authorization');

		//other
		$data['gmap_key'] = $this->config->item('gmap_key');

		//templating
		$data['page_title'] = 'Outlet List';
		$this->template->view('outlet_v', $data);
	}

	/**
	 * Delete sales tag outlet
	 */
	public function delete_sales_tag_outlet($sales_tag_id)
	{
		$delete = $this->Outlet_model->delete_sales_tag($sales_tag_id);
		if ($delete) {
			echo format_json(array(
				'status' => 200,
				'message' => 'Delete sales tag success'
			));
			die();
		} else {
			echo format_json(array(
				'status' => 500,
				'message' => 'Delete sales tag failed'
			));
			die();
		}
	}

	public function datatables()
	{
		echo $this->Outlet_model->datatables();
	}

	public function _rules($action)
	{
		//validation form
		$val = $this->form_validation;

		if ($action == 'update') {
			$val->set_rules('id', 'outlet_id', 'trim|callback_validate_outlet');
		}


		$val
			->set_rules('name', 'outlet name', 'trim|required|max_length[50]')
			->set_rules('address', 'address', 'trim|required|max_length[100]')
			->set_rules('phone', 'phone', 'trim|required|max_length[15]|numeric')
			->set_rules('country', 'country', 'trim|max_length[30]')
			->set_rules('province', 'province', 'trim|max_length[50]')
			->set_rules('city', 'city', 'trim|max_length[50]')
			->set_rules('postal_code', 'postal code', 'trim|max_length[6]')
			->set_rules('app_show', 'show on app', 'trim|required|in_list[1,0]', array(
				'in_list' => 'The %s must yes or no.'
			))
			->set_error_delimiters('', '');

		//coordinate
		$this->form_validation->set_rules('lat', 'lat', 'trim');
		$this->form_validation->set_rules('long', 'long', 'trim');

		//image
		$this->form_validation->set_rules('outlet_logo', 'outlet logo', 'callback_validate_image_logo');
		$this->form_validation->set_rules('receipt_logo', 'receipt logo', 'callback_validate_image_receipt');

		//working hour
		$this->form_validation->set_rules('workinghour', 'working hour', 'trim|in_list[alwaysopen,selectedhour]', array(
			'in_list' => 'The %s must working hour or selected hour.'
		));
		$this->form_validation->set_rules('workinghour_alwaysopen[]', 'workinghour', 'trim');
		$this->form_validation->set_rules('workinghour_selectedhour[]', 'workinghour', 'trim');

		//sharing
		$this->form_validation->set_rules('commission_sharing', 'commission sharing', 'trim|numeric|greater_than_equal_to[0]|less_than_equal_to[100]');
	}

	public function create()
	{
		//cek privilege
		if (!$this->privilege->check($this->url_path, 'add')) {
			echo format_json(array(
				'status' => 'error',
				'message' => 'Access Forbidden!'
			));
			die();
		}

		//init
		$draw_json = array();
		$msg['success'] = 'Create Outlet Success';
		$msg['error']	= 'Create Outlet Failed';

		//validation
		$this->_rules('create');
		if ($this->form_validation->run() == FALSE) {
			$draw_json = array(
				'status' => 'error',
				'message' => $msg['error'],
				'data' => $this->form_validation->error_array()
			);
		} else {
			//create new outlet
			$version = date('YmdHis');
			$dataInsert = [
				'name' => $this->input->post('name', true),
				'address' => $this->input->post('address', true),
				'phone' => $this->input->post('phone', true),
				'app_show' => $this->input->post('app_show', true),
				'country' => $this->input->post('country', true),
				'province' => $this->input->post('province', true),
				'city' => $this->input->post('city', true),
				'postal_code' => $this->input->post('postal_code', true),
				'feature' => $this->_process_outletfeature(),

				//coordinate
				'latitude' => $this->input->post('lat', true),
				'longitude' => $this->input->post('long', true),

				//receipt
				'receipt_note' => $this->input->post('receipt_note', true),
				'receipt_phone' => $this->input->post('receipt_phone', true),
				'receipt_address' => $this->input->post('receipt_address', true),
				'receipt_socialmedia' => $this->input->post('receipt_socialmedia', true),

				//commission
				'commission_sharing' => $this->input->post('commission_sharing', true),
			];

			//insert DB
			$this->db->trans_start();
			$response = $this->Outlet_model->create($dataInsert);
			if ($response) {
				$outlet_id = $this->db->insert_id();

				//WORKING HOUR PROCESS
				$data_workinghour = array();
				switch ($this->input->post('workinghour')) {
					case 'alwaysopen':
						$input = $this->input->post('workinghour_alwaysopen[]');
						if (!empty($input)) {
							foreach ($input as $key => $value) {
								if ($this->daylist[$key]) {
									$data_workinghour[] = array(
										'outlet_fkid'	=> $outlet_id,
										'day'			=> $key,
										'time_open'		=> '00:00:00',
										'time_close'	=> '23:59:59',
									);
								}
							}
						}
						break;
					case 'selectedhour':
						$input = $this->input->post('workinghour_selectedhour[]');
						if (!empty($input)) {
							foreach ($input as $key => $value) {
								$input_active = $this->input->post('workinghour_selectedhour_active[' . $key . ']');
								if ($this->daylist[$key] && $input_active) {
									$input2 = $this->input->post('workinghour_selectedhour[' . $key . ']');
									if (!empty($input2)) {
										foreach ($input2 as $v2) {
											$v2 = str_replace(' ', '', $v2);
											$time_selected = explode('-', $v2);
											$time_open = (!empty($time_selected[0])) ? $time_selected[0] : '';
											$time_close = (!empty($time_selected[1])) ? $time_selected[1] : '';

											//tampung data
											$data_workinghour[] = array(
												'outlet_fkid'	=> $outlet_id,
												'day'			=> $key,
												'time_open'		=> $time_open,
												'time_close'	=> $time_close,
											);
										}
									}
								}
							}
						}
						break;
					default:
						break;
				}

				//insert working hour
				if (!empty($data_workinghour)) {
					$this->Outlet_model->createbatch_workinghour($data_workinghour);
				}

				//SOCIAL MEDIA PROCESS
				$data_socialmedia = array();
				$input = $this->input->post('socialmedia[]');
				if (!empty($input)) {
					foreach ($input as $key => $value) {
						//sosmed list
						if (!empty($this->sosmedlist[$key]) && $key != 'other') {
							$sosmed_type = $key;
							$sosmed_address = $this->input->post('socialmedia[' . $key . ']', true);

							//simpan ke temporary
							$data_socialmedia[] = array(
								'outlet_fkid' => $outlet_id,
								'social_type' => $sosmed_type,
								'social_address' => $sosmed_address,
							);
						} else {
							$input2 = $this->input->post('socialmedia[' . $key . ']');
							$sosmed_type = $input2['name'];
							$sosmed_address = $input2['value'];

							//simpan ke temporary
							$data_socialmedia[] = array(
								'outlet_fkid' => $outlet_id,
								'social_type' => $sosmed_type,
								'social_address' => $sosmed_address,
							);
						}
					}
				}

				if (!empty($data_socialmedia)) {
					$this->Outlet_model->createbatch_socialmedia($data_socialmedia);
				}

				/**
				 * Insert order type
				 */
				$order_types = [];
				$insert_type_data = [];
				if (!empty($this->input->post("order_type"))) {
					$order_types = json_decode($this->input->post("order_type"));
					foreach ($order_types as $value) {
						array_push($insert_type_data, [
							"name" => $value->name,
							"data_status" => $value->data_status,
							"admin_fkid" => $this->session->userdata('admin_id'),
							"date_created" => round(microtime(true) * 1000),
							"date_modified" => round(microtime(true) * 1000),
						]);
					}
					$this->Outlet_model->insert_sales_tag($insert_type_data, $outlet_id);
				}

				//UPLOAD GAMBAR outlet logo dan receipt (update DB dengan url gambar)
				$path = ENVIRONMENT . '/outlets/' . $this->session->userdata('admin_id') . '/';
				$version = date('YmdHis');

				$data_upload = [
					'outlet_logo' => $this->google_storage->upload('outlet_logo', $path, $outlet_id . '.logo.' . $version),
					'receipt_logo' => $this->google_storage->upload('receipt_logo', $path, $outlet_id . '.receipt.' . $version)
				];
				$this->Outlet_model->update($outlet_id, $data_upload);


				//tambah outlet access kalau yang menambahkan employee
				if ($this->session->userdata('user_type') == 'employee') {
					$this->db->insert('employee_outlet', [
						'employee_fkid' => $this->session->userdata('user_id'),
						'outlet_fkid' => $outlet_id
					]);
					$this->privilege->refresh_outlet_access(); //regenerate session role
				}
			}
			$r = $this->db->trans_complete();

			if ($r) {
				$draw_json = array(
					'status' => 'success',
					'message' => $msg['success'],
				);
			} else {
				$draw_json = array(
					'status' => 'error',
					'message' => $msg['error'],
				);
			}
		} //validation


		//response
		echo format_json($draw_json);
	}

	public function get($id = null)
	{
		//cek privilege
		if (!$this->privilege->check($this->url_path, 'edit')) {
			echo format_json(array(
				'status' => 'error',
				'message' => 'Access Forbidden!'
			));
			die();
		}
		if (!$this->privilege->check_outlet_access($id)) {
			echo format_json(array(
				'status' => 'error',
				'message' => 'Outlet Access Forbidden!'
			));
			die();
		}


		//init
		$draw_json = array();

		//cek data
		$row = $this->Outlet_model->get_by_id($id)->row();
		if ($row) {
			$draw_json = array(
				'status' => 'success',
				'data' => array(
					'id' => $row->outlet_id,
					'name' => $row->name,
					'address' => $row->address,
					'country' => $row->country,
					'province' => $row->province,
					'city' => $row->city,
					'postal_code' => $row->postal_code,
					'phone' => $row->phone,
					'app_show' => $row->app_show,
					'outlet_logo' => $this->check_image_exist($row->outlet_logo),

					//outlet feature
					'feature' => json_decode($row->feature),

					//receipt
					'receipt' => array(
						'receipt_note' => $row->receipt_note,
						'receipt_phone' => $row->receipt_phone,
						'receipt_address' => $row->receipt_address,
						'receipt_socialmedia' => $row->receipt_socialmedia,
						'receipt_logo' => $this->check_image_exist($row->receipt_logo),
					),

					//location
					'location' => array(
						'lat' => $row->latitude,
						'long' => $row->longitude,
					),

					//commission
					'commission_sharing' => $row->commission_sharing ?? 0,
				)
			);

			//working hours
			$temp_workhour = array();
			$workinghour = '';
			$workhour = $this->Outlet_model->get_workinghour($id)->result();
			if (!empty($workhour)) {
				foreach ($workhour as $r) {
					//cek day
					if (!empty($this->daylist[$r->day])) {
						//cek apakah 24 jam
						if ($r->time_open == '00:00:00' && $r->time_close == '23:59:59' && $workinghour != 'selectedhour') {
							$workinghour = 'alwaysopen';
						} else {
							$workinghour = 'selectedhour';
						}

						//save to temp
						$temp_workhour[$r->day][] = array(
							'time_open' => date('H:i', strtotime($r->time_open)), //$r->time_open,
							'time_close' => date('H:i', strtotime($r->time_close)), //$r->time_close
						);
					}
				}
			}
			$draw_json['data']['workinghour'] = $workinghour;
			$draw_json['data']['workinghour_list'] = $temp_workhour;


			//outlet sosmed
			$temp_sosmed = array();
			$sosmed = $this->Outlet_model->get_socialmedia($id)->result();
			foreach ($sosmed as $value) {
				$type = $value->social_type;
				if (!empty($this->sosmedlist[$type])) {
					$temp_sosmed[$type] = $value->social_address;
				} else {
					$temp_sosmed['other'][$type] = $value->social_address;
				}
			}
			$draw_json['data']['socialmedia'] = $temp_sosmed;

			/**
			 * SALES TAG OUTLET
			 */
			$sales_tags = $this->Outlet_model->get_sales_tag_outlet($id)->result();
			$draw_json['data']['sales_outlet_tags'] = $sales_tags;
		} else {
			$draw_json = array(
				'status' => 'error',
				'message' => 'Record Not Found'
			);
		}

		//response
		echo format_json($draw_json);
	}

	/**
	 * GET SALES TAG
	 */

	public function update()
	{
		//init
		$id = $this->input->post('id', true);
		$order_types = [];
		$update_type_data = [];
		$insert_type_data = [];
		if (!empty($this->input->post("order_type"))) {
			$order_types = json_decode($this->input->post("order_type"));
			foreach ($order_types as $value) {
				if ($value->sales_tag_id != "") {
					array_push($update_type_data, $value);
				} else {
					array_push($insert_type_data, [
						"name" => $value->name,
						"data_status" => $value->data_status,
						"admin_fkid" => $value->admin_fkid,
						"date_created" => round(microtime(true) * 1000),
						"date_modified" => round(microtime(true) * 1000),
					]);
				}
			}
		}

		//cek privilege
		//versi terbaru proteksi dengan ajax
		// if (!$this->privilege->check( $this->url_path, 'edit')) {
		// 	echo format_json(array(
		// 		'status' => 'error',
		// 		'message' => 'Access Forbidden!'
		// 	));
		// 	die();
		// }
		if (!$this->privilege->check_outlet_access($id)) {
			echo format_json(array(
				'status' => 'error',
				'message' => 'Outlet Access Forbidden!'
			));
			die();
		}


		$row = $this->Outlet_model->get_by_id($id)->row();

		//init
		$draw_json = array();
		$msg['success'] = 'Update Outlet Success';
		$msg['error']	= 'Update Outlet Failed';

		//validation form
		$this->_rules('update');
		if ($this->form_validation->run() == FALSE) {
			$draw_json = array(
				'status' => 'error',
				'message' => $msg['error'],
				'data' => $this->form_validation->error_array()
			);
		} else {
			//update outlet
			$dataUpdate = [
				'name' => $this->input->post('name', true),
				'address' => $this->input->post('address', true),
				'phone' => $this->input->post('phone', true),
				'app_show' => $this->input->post('app_show', true),
				'country' => $this->input->post('country', true),
				'province' => $this->input->post('province', true),
				'city' => $this->input->post('city', true),
				'postal_code' => $this->input->post('postal_code', true),
				'feature' => $this->_process_outletfeature(),

				//coordinate
				'latitude' => $this->input->post('lat', true),
				'longitude' => $this->input->post('long', true),

				//receipt
				'receipt_note' => $this->input->post('receipt_note', true),
				'receipt_phone' => $this->input->post('receipt_phone', true),
				'receipt_address' => $this->input->post('receipt_address', true),
				'receipt_socialmedia' => $this->input->post('receipt_socialmedia', true),

				//image
				// 'outlet_logo' => $this->_process_upload_image('outlet_logo', $outlet_name.'.logo.'.$version),
				// 'receipt_logo' => $this->_process_upload_image('receipt_logo', $outlet_name.'.receipt.'.$version),

				//commission
				'commission_sharing' => $this->input->post('commission_sharing', true),
			];

			//init
			$version = date('YmdHis');
			$path = ENVIRONMENT . '/outlets/' . $this->session->userdata('admin_id') . '/'; //storage path


			//update gambar outlet logo
			if ($this->input->post('outlet_logo_url') != $row->outlet_logo) {
				if (!empty($_FILES['outlet_logo']['name'])) {
					// echo "logo diganti";
					$dataUpdate['outlet_logo'] = $this->google_storage->upload('outlet_logo', $path, $id . '.logo.' . $version);
				} else {
					// echo "logo recipt dihapus";
					$dataUpdate['outlet_logo'] = '';
				}
			}

			//update gambar receipt logo
			if ($this->input->post('receipt_logo_url') != $row->receipt_logo) {
				if (!empty($_FILES['receipt_logo']['name'])) {
					// echo "logo diganti";
					$dataUpdate['receipt_logo'] = $this->google_storage->upload('receipt_logo', $path, $id . '.receipt.' . $version);
				} else {
					// echo "logo recipt dihapus";
					$dataUpdate['receipt_logo'] = '';
				}
			}

			//update DB
			$this->db->trans_start();
			$response = $this->Outlet_model->update($id, $dataUpdate);
			if ($response) {
				$outlet_id = $id;

				//WORKING HOUR PROCESS START
				$this->Outlet_model->delete_workinghour_by_outletID($outlet_id); //delete old working hour
				$data_workinghour = array();
				switch ($this->input->post('workinghour')) {
					case 'alwaysopen':
						$input = $this->input->post('workinghour_alwaysopen[]');
						if (!empty($input)) {
							foreach ($input as $key => $value) {
								if ($this->daylist[$key]) {
									$data_workinghour[] = array(
										'outlet_fkid'	=> $outlet_id,
										'day'			=> $key,
										'time_open'		=> '00:00:00',
										'time_close'	=> '23:59:59',
									);
								}
							}
						}
						break;
					case 'selectedhour':
						$input = $this->input->post('workinghour_selectedhour[]');
						if (!empty($input)) {
							foreach ($input as $key => $value) {
								$input_active = $this->input->post('workinghour_selectedhour_active[' . $key . ']');
								if ($this->daylist[$key] && $input_active) {
									$input2 = $this->input->post('workinghour_selectedhour[' . $key . ']');
									if (!empty($input2)) {
										foreach ($input2 as $v2) {
											$v2 = str_replace(' ', '', $v2);
											$time_selected = explode('-', $v2);
											$time_open = (!empty($time_selected[0])) ? $time_selected[0] : '';
											$time_close = (!empty($time_selected[1])) ? $time_selected[1] : '';

											//tampung data
											$data_workinghour[] = array(
												'outlet_fkid'	=> $outlet_id,
												'day'			=> $key,
												'time_open'		=> $time_open,
												'time_close'	=> $time_close,
											);
										}
									}
								}
							}
						}
						break;
					default:
						break;
				}

				//insert working hour
				if (!empty($data_workinghour)) {
					$this->Outlet_model->createbatch_workinghour($data_workinghour);
				}
				//WORKING HOUR PROCESS END


				//SOCIAL MEDIA PROCESS START
				$this->Outlet_model->delete_socialmedia_by_outletID($outlet_id); //delete old social media
				$data_socialmedia = array();
				$input = $this->input->post('socialmedia[]');
				if (!empty($input)) {
					foreach ($input as $key => $value) {
						//sosmed list
						if (!empty($this->sosmedlist[$key]) && $key != 'other') {
							$sosmed_type = $key;
							$sosmed_address = $this->input->post('socialmedia[' . $key . ']', true);

							//simpan ke temporary
							$data_socialmedia[] = array(
								'outlet_fkid' => $outlet_id,
								'social_type' => $sosmed_type,
								'social_address' => $sosmed_address,
							);
						} else {
							$input2 = $this->input->post('socialmedia[' . $key . ']');
							$sosmed_type = $input2['name'];
							$sosmed_address = $input2['value'];

							//simpan ke temporary
							$data_socialmedia[] = array(
								'outlet_fkid' => $outlet_id,
								'social_type' => $sosmed_type,
								'social_address' => $sosmed_address,
							);
						}
					}
				}

				if (!empty($data_socialmedia)) {
					$this->Outlet_model->createbatch_socialmedia($data_socialmedia);
				}
				//SOCIAL MEDIA PROCESS END

				/**
				 * UPDATE ORDER TYPE
				 */
				if (count($update_type_data) > 0) {
					$this->Outlet_model->update_sales_tag($update_type_data);
				}

				/**
				 * INSERT ORDER TYPE
				 */
				if (count($insert_type_data) > 0) {
					$this->Outlet_model->insert_sales_tag($insert_type_data, $id);
				}
			}
			$r = $this->db->trans_complete();
			if ($r) {
				//hapus gambar lama
				if ($this->input->post('outlet_logo_url') != $row->outlet_logo) {
					$this->google_storage->delete($row->outlet_logo);
				}
				if ($this->input->post('receipt_logo_url') != $row->receipt_logo) {
					$this->google_storage->delete($row->receipt_logo);
				}

				$draw_json = array(
					'status' => 'success',
					'message' => $msg['success'],
				);
			} else {
				$draw_json = array(
					'status' => 'error',
					'message' => $msg['error'],
				);
			}
		} //validation


		//response
		echo format_json($draw_json);
	}

	public function delete($id = null, $type = '')
	{
		//cek privilege
		if (!$this->privilege->check($this->url_path, 'delete')) {
			echo format_json(array(
				'status' => 'error',
				'message' => 'Access Forbidden!'
			));
			die();
		}
		if (!$this->privilege->check_outlet_access($id)) {
			echo format_json(array(
				'status' => 'error',
				'message' => 'Outlet Access Forbidden!'
			));
			die();
		}


		//init
		$draw_json = array();

		//cek data
		$row = $this->Outlet_model->get_by_id($id)->row();
		if ($row) {
			switch ($type) {
				case 'permanent':
					//update to status off
					$response = $this->Outlet_model->update($id, ['data_status' => 'off']);
					if ($response) {
						//hapus gambar dari bucket
						$this->google_storage->delete($row->outlet_logo);
						$this->google_storage->delete($row->receipt_logo);

						//set response
						$draw_json = array(
							'status' => 'success',
							'message' => 'Delete Outlet Success'
						);
					}
					break;

				default:
					$response = $this->Outlet_model->delete($id);
					if ($response) {

						//hapus gambar dari direktori
						// $filename = $this->outlet_photo_path.$row->receipt_logo;
						// if (file_exists($filename) && (!empty($row->receipt_logo))) {
						// 	unlink($filename); //echo "The file $filename exists";
						// }

						//hapus gambar dari bucket
						$this->google_storage->delete($row->outlet_logo);
						$this->google_storage->delete($row->receipt_logo);

						//menambah notifikasi dari hapus data
						$draw_json = array(
							'status' => 'success',
							'message' => 'Delete Record Success'
						);
					} else {
						$draw_json = array(
							'status' => 'warning',
							'message' => 'Outlet in Use'
						);
					}
					break;
			}
		} else {
			$draw_json = array(
				'status' => 'error',
				'message' => 'Record Not Found'
			);
		}

		//response output
		echo format_json($draw_json);
	}

	public function _process_outletfeature()
	{
		//mengetahui feature yang dicentang
		//MAIN FEATURE
		$checked_feature = array();
		$featurelist = $this->outlet_feature->featurelist('mainfeature');
		foreach ($featurelist as $list) {
			$checked = ($this->input->post('feature[' . $list[0] . ']') == 'on') ? true : false;
			$checked_feature[$list[0]] = $checked;
		}
		//--FEATURE AUTHORIZATION
		$checked_feature['authorization'] = array();
		$featurelist_authorization = $this->outlet_feature->featurelist('authorization');
		foreach ($featurelist_authorization as $list) {
			$checked = ($this->input->post('feature[authorization][' . $list[0] . ']') == 'on') ? true : false;
			if ($checked === true) {
				$checked_feature['authorization'][$list[0]] = true;
			}
		}
		return json_encode($checked_feature);
	}

	public function validate_image_logo()
	{
		if (!empty($_FILES['outlet_logo']['name'])) {
			$file_ext = pathinfo($_FILES['outlet_logo']['name'], PATHINFO_EXTENSION);
			$filename = password_hash(date('YmdHis'), PASSWORD_DEFAULT);
			$config['file_name'] = 'outletlogo_' . preg_replace("/\W|_/", "", $filename) . '.' . $file_ext;
			$config['upload_path'] = $this->upload_path;
			$config['allowed_types'] = 'gif|jpg|jpeg|png';
			$config['max_size']  = '100';
			// $config['max_width']  = '1024';
			// $config['max_height']  = '768';

			$this->load->library('upload', $config);

			if (!$this->upload->do_upload('outlet_logo')) {
				$error_message = $this->upload->display_errors();
				$error_message = str_replace('<p>', '', $error_message);
				$error_message = str_replace('</p>', '', $error_message);
				$this->form_validation->set_message('validate_image_logo', $error_message);
				return false;
			} else {
				$data = $this->upload->data();
				unlink($data['full_path']);
				return true;
			}
		} else {
			return true;
		}
	}

	public function validate_image_receipt()
	{
		if (!empty($_FILES['receipt_logo']['name'])) {
			$file_ext = pathinfo($_FILES['receipt_logo']['name'], PATHINFO_EXTENSION);
			$filename = password_hash(date('YmdHis'), PASSWORD_DEFAULT);
			$config['file_name'] = 'receiptlogo_' . preg_replace("/\W|_/", "", $filename) . '.' . $file_ext;
			$config['upload_path'] = $this->upload_path;
			$config['allowed_types'] = 'gif|jpg|jpeg|png';
			$config['max_size']  = '100';
			// $config['max_width']  = '1024';
			// $config['max_height']  = '768';

			$this->load->library('upload', $config);

			if (!$this->upload->do_upload('receipt_logo')) {
				$error_message = $this->upload->display_errors();
				$error_message = str_replace('<p>', '', $error_message);
				$error_message = str_replace('</p>', '', $error_message);
				$this->form_validation->set_message('validate_image_receipt', $error_message);
				return false;
			} else {
				$data = $this->upload->data();
				unlink($data['full_path']);
				return true;
			}
		} else {
			return true;
		}
	}

	public function validate_outlet($val)
	{
		$outlet_id = $val;
		$row = $this->Outlet_model->get_by_id($outlet_id);
		if ($row) {
			return true;
		} else {
			$this->form_validation->set_message('validate_outlet', 'Outlet not found.');
			return false;
		}
	}

	private function check_image_exist($filename = '')
	{
		$image_url = '';

		//cek file di local
		if (empty($image_url) && !empty($filename)) {

			$local_imagepath = $this->outlet_img_oldpath . $filename;
			if (file_exists($local_imagepath)) {
				$image_url = str_replace(FCPATH, '', $local_imagepath);
				$image_url = base_url($image_url);
			}
		}

		//cek file di google storage
		if (empty($image_url) && !empty($filename)) {

			$headers = @get_headers($filename);
			if ($headers && strpos($headers[0], '200')) {
				$image_url = $filename;
			}
		}

		return $image_url;
	}
}

/* End of file Outletlist.php */
/* Location: ./application/modules/outlet_list/controllers/Outletlist.php */