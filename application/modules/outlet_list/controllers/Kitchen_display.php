<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Kitchen_display extends Auth_Controller
{
  public function __construct()
  {
    parent::__construct();
    $this->load->model("Kds_model");
  }

  public function index()
  {
    $categories = $this->Kds_model->get_product_subcategories()->result();
    $outlets = $this->Kds_model->get_outlets_opts()->result();

    $data = [
      "page_title" => "Outlet - Kitchen Display",
      "url_datatables" => current_url() . "/datatables",
      "categories" => json_encode($categories),
      "url_delete" => current_url() . "/delete_kds",
      "url_update" => current_url() . "/update_kds",
      "outlets" => json_encode($outlets),
    ];
    $this->template->view("kitchen_display/index", $data);
  }

  public function datatables()
  {
    $selectedOtl = $this->input->post("selected_outlets");
    echo $this->Kds_model->datatables($selectedOtl);
  }

  public function delete_kds($id)
  {
    $delete = $this->Kds_model->deleteKds($id);
    if ($delete) {
      echo json_encode([
        "status" => 200,
        "message" => "Delete Kitchen Display Success",
      ]);
    } else {
      echo json_encode([
        "status" => 500,
        "message" => "Delete Kitchen Display Failed",
      ]);
    }
  }

  public function update_kds($id)
  {
    $data = $this->input->post();

    $arrCat = explode(",", $data["categories"]);
    $categories = $this->Kds_model->get_product_subcategories()->result();
    $kitchenCat = [];
    foreach ($arrCat as  $value) {
      foreach ($categories as $val) {
        if ($value == $val->product_subcategory_id) {
          array_push($kitchenCat, ["category_id" => $value, "category_name" => $val->name]);
        }
      }
    }

    $data["categories"] = json_encode($kitchenCat);

    $update = $this->Kds_model->updateKds($id, $data);
    if ($update) {
      echo json_encode([
        "status" => 200,
        "message" => "Update Kitchen Display Success"
      ]);
    } else {
      echo json_encode([
        "status" => 500,
        "message" => "Update Kitchen Display Failed"
      ]);
    }
  }
}
