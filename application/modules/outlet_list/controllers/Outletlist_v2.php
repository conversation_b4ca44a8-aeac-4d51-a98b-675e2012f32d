<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Outletlist_v2 extends Auth_Controller {

	protected $url_path = 'outlets/outletlist';
	protected $daylist = [
		'sunday' => 'Sunday',
		'monday' => 'Monday',
		'tuesday' => 'Tuesday',
		'wednesday' => 'Wednesday',
		'thursday' => 'Thursday',
		'friday' => 'Friday',
		'saturday' => 'Saturday'
	];

	public function __construct()
	{
		parent::__construct();
		$this->load->model('Outlet_model');
		$this->load->library('google/Google_storage');
	}

	public function index()
	{
		$data['url_api'] = $this->api.'/outlets/v1/outlet';
		$data['url_datatable'] = $this->api.'/outlets/v1/outlet/datatable';
		$data['url_modal_bulkupdate'] = site_url('outlets/outletlist_v2/modal_bulkupdate');

		//feature access / permission access
		$data['permission'] = array(
			'add' => $this->privilege->check($this->url_path, 'add'),
			'edit' => $this->privilege->check($this->url_path, 'edit'),
			'delete' => $this->privilege->check($this->url_path, 'delete'),
		);

		//outlet feature
		$data['list_outlet_feature'] = $this->outlet_feature();
		$data['list_day'] = $this->daylist;

		//other
		$data['gmap_key'] = $this->config->item('gmap_key');

		// templating
		// ------------------------------------------------------------------------
		$data['page_title'] = 'Outlet List';
		if (ENVIRONMENT != 'production') {
			$data['page_title'] .= ' v2';
		}
		$this->template->view('v2/outlet_v2', $data);
	}

	public function modal_bulkupdate()
	{
		//cek privilege
		if (!$this->privilege->check('outlets/outletlist', 'edit')) {
			http_response_code(403);
			echo json([
				'message' => 'Access Forbidden!'
			]);
			die;
		}

		//list outlet
		$data['list_outlets'] =  $this->Outlet_model->get_all()->result_array();

		//feature
		$data['list_outlet_feature'] = $this->outlet_feature();

		//form url
		$data['form_url'] = $this->api.'/outlets/v1/outlet/bulk';

		$this->load->view('v2/form_bulkupdate_v2', $data);
	}

	protected function outlet_feature()
	{
		//feature
		$list_outlet_feature = [];
		$list_outlet_feature_main = $this->outlet_feature->featurelist('mainfeature');
		foreach ($list_outlet_feature_main as $index => $value) {
			$key = $value[0];
			$text = $value[1];
			$desc = $value[2];

			//get sub menu
			$list_outlet_feature_sub = [];
			$list_outlet_feature_sub_tmp = $this->outlet_feature->featurelist($key);
			foreach ($list_outlet_feature_sub_tmp as $index2 => $value2) {
				$key2 = $value2[0];
				$text2 = $value2[1];
				$desc2 = $value2[2] ?? '';

				$list_outlet_feature_sub[$key2] = [
					'key' => $key2,
					'text' => $text2,
					'description' => $desc2,
				];
			}

			//append data
			$list_outlet_feature[$key] = [
				'key' => $key,
				'text' => $text,
				'description' => $desc,
				'sub' => $list_outlet_feature_sub,
			];
		}

		return $list_outlet_feature;
	}

}

/* End of file Outletlist_v2.php */
/* Location: ./application/modules/outlet_list/controllers/Outletlist_v2.php */