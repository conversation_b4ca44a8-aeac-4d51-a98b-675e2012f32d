<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Outlet_bulkupdate extends Auth_Controller {

	protected $valid_list_outlets = [];

	public function __construct()
	{
		parent::__construct();
		$this->load->model('Outlet_model');
		$this->load->library('google/Google_storage');
	}

	public function index_modal()
	{
		//cek privilege
		if (!$this->privilege->check('outlets/outletlist', 'edit')) {
			http_response_code(403);
			echo format_json(array(
				'status' => 'error',
				'message' => 'Access Forbidden!'
			));
			die();
		}

		//list outlet
		$data['list_outlets'] =  $this->Outlet_model->get_all()->result_array();

		//feature
		$data['featurelist'] = $this->outlet_feature->featurelist('mainfeature');
		$data['featurelist2']['authorization'] = $this->outlet_feature->featurelist('authorization');

		//form url
		$data['form_url'] = site_url('outlets/outletlist/update_batch');

		$this->load->view('outlet_form_bulkupdate_v', $data);
	}

	public function update()
	{
		//cek privilege
		if (!$this->privilege->check('outlets/outletlist', 'edit')) {
			http_response_code(403);
			echo format_json(array(
				'status' => 'error',
				'message' => 'Access Forbidden!'
			));
			die();
		}
		// ------------------------------------------------------------------------


		//handle input outlet
		$input_outlets = $this->input->post('outlets[]');
		if (!empty($input_outlets)) {
			$input_outlets = implode(',', $input_outlets);
		}
		$_POST['outlets'] = $input_outlets;

		$this->form_validation->set_rules('outlets', 'outlet', 'trim|required|callback_validate_outlet');
		$this->form_validation->set_rules('receipt_note', 'receipt note', 'trim');
		$this->form_validation->set_rules('receipt_phone', 'receipt phone', 'trim|max_length[15]|numeric');
		$this->form_validation->set_rules('receipt_address', 'receipt address', 'trim|max_length[100]');
		$this->form_validation->set_rules('receipt_socialmedia', 'receipt social media', 'trim|max_length[100]');
		$this->form_validation->set_rules('receipt_logo', 'receipt logo', 'callback_validate_image_receipt');
		if ($this->form_validation->run() == FALSE) {
			$errors = $this->form_validation->error_array();

			//olah data error
			// $errors['outlets'] = $errors['outlets[]'];
			// unset($errors['outlets[]']);

			http_response_code(400);
			echo format_json([
				'message' => 'Invalid Input',
				'errors' => $errors,
			]);
			die;
		}


		//update process
		// ------------------------------------------------------------------------
		$this->db->trans_start();
		//get outlet
		$list_outlets = $this->valid_list_outlets;
		$outlet_ids = array_column($list_outlets, 'outlet_id');

		//get receipt logo
		$receipt_logo = null;
		if (!empty($_FILES['receipt_logo']['name'])) {
			$path = ENVIRONMENT . '/outlets/' . $this->session->userdata('admin_id') . '/';
			$version = date('YmdHis');
			$receipt_logo = $this->google_storage->upload('receipt_logo', $path, 'batch_update.receipt.' . $version);
		}
		

		//update outlet feature + receipt
		$object = [
			'feature' => $this->_process_outletfeature(),
			'receipt_note' => $this->input->post('receipt_note'),
			'receipt_phone' => $this->input->post('receipt_phone'),
			'receipt_address' => $this->input->post('receipt_address'),
			'receipt_socialmedia' => $this->input->post('receipt_socialmedia'),
		];
		if (!empty($receipt_logo)) {
			$object['receipt_logo'] = $receipt_logo;
		}
		$this->db->where_in('outlet_id', $outlet_ids);
		$this->db->update('outlets', $object);

		$complete = $this->db->trans_complete();
		if ($complete) {
			$total_outlet = count($outlet_ids);
			$outlet_spell = ($total_outlet) > 1 ? 'Outlets' : 'Outlet';
			http_response_code(200);
			echo format_json([
				'message' => 'Update to '.$total_outlet.' '.$outlet_spell.' Success'
			]);
			die;
		}

		http_response_code(500);
		echo format_json(['message' => 'Internal Server Error [422]']);
	}



	// PRIVATE PROCESS
	// ------------------------------------------------------------------------
	private function _process_outletfeature()
	{
		//mengetahui feature yang dicentang
		//MAIN FEATURE
		$checked_feature = array();
		$featurelist = $this->outlet_feature->featurelist('mainfeature');
		foreach ($featurelist as $list) {
			$checked = ($this->input->post('feature[' . $list[0] . ']') == 'on') ? true : false;
			$checked_feature[$list[0]] = $checked;
		}
		//--FEATURE AUTHORIZATION
		$checked_feature['authorization'] = array();
		$featurelist_authorization = $this->outlet_feature->featurelist('authorization');
		foreach ($featurelist_authorization as $list) {
			$checked = ($this->input->post('feature[authorization][' . $list[0] . ']') == 'on') ? true : false;
			if ($checked === true) {
				$checked_feature['authorization'][$list[0]] = true;
			}
		}
		return json_encode($checked_feature);
	}


	// CUSTOM FORM VALIDATION
	// ------------------------------------------------------------------------
	public function validate_outlet($value='')
	{
		if (empty($value)) {
			return TRUE;
		}

		$list_outlets = [];
		$outlet_ids = explode(',', $value);
		if ($this->session->userdata('user_type') == 'employee') {
			$outlet_ids = array_intersect($outlet_ids, $this->session->userdata('outlet_access'));
		}
		if (!empty($outlet_ids)) {
			$list_outlets = $this->Outlet_model->get_by_ids($outlet_ids)->result_array();
		}
		if (!$list_outlets) {
			$this->form_validation->set_message('validate_outlet', 'The %s is invalid.');
			return FALSE;
		}

		//send to temporary variable
		$this->valid_list_outlets = $list_outlets;

		return TRUE;
	}

	public function validate_image_receipt($value='')
	{
		if (!empty($_FILES['receipt_logo']['name'])) {
			$file_ext = pathinfo($_FILES['receipt_logo']['name'], PATHINFO_EXTENSION);
			$filename = password_hash(date('YmdHis'), PASSWORD_DEFAULT);
			$config['file_name'] = 'receiptlogo_' . preg_replace("/\W|_/", "", $filename) . '.' . $file_ext;
			$config['upload_path'] = sys_get_temp_dir().'/';
			$config['allowed_types'] = 'jpg|jpeg|png';
			$config['max_size']  = '100';
			// $config['max_width']  = '1024';
			// $config['max_height']  = '768';

			$this->load->library('upload', $config);

			if (!$this->upload->do_upload('receipt_logo')) {
				$error_message = $this->upload->display_errors();
				$error_message = str_replace('<p>', '', $error_message);
				$error_message = str_replace('</p>', '', $error_message);
				$this->form_validation->set_message('validate_image_receipt', $error_message);
				return false;
			} else {
				$data = $this->upload->data();
				unlink($data['full_path']);
				return true;
			}
		} else {
			return true;
		}
	}

}

/* End of file Outlet_bulkupdate.php */
/* Location: ./application/modules/outlet_list/controllers/Outlet_bulkupdate.php */