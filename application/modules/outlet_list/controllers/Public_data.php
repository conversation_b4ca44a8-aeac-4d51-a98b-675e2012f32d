<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Public_data extends CI_Controller {

	public function index()
	{
		echo format_json([
			'message' => 'Success',
			'data' => $this->outlet_feature(),
		]);
	}

	protected function outlet_feature()
	{
		//feature
		$list_outlet_feature = [];
		$list_outlet_feature_main = $this->outlet_feature->featurelist('mainfeature');
		foreach ($list_outlet_feature_main as $index => $value) {
			$key = $value[0];
			$text = $value[1];

			//get sub menu
			$list_outlet_feature_sub = [];
			$list_outlet_feature_sub_tmp = $this->outlet_feature->featurelist($key);
			foreach ($list_outlet_feature_sub_tmp as $index2 => $value2) {
				$key2 = $value2[0];
				$text2 = $value2[1];

				$list_outlet_feature_sub[$key2] = [
					'key' => $key2,
					'text' => $text2,
				];
			}

			//append data
			$list_outlet_feature[$key] = [
				'key' => $key,
				'text' => $text,
				'sub' => $list_outlet_feature_sub,
			];
		}

		return $list_outlet_feature;
	}

}

/* End of file Public_data.php */
/* Location: ./application/modules/outlet_list/controllers/Public_data.php */